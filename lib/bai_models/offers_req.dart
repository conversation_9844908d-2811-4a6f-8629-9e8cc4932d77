import 'dart:convert';
import 'dart:typed_data';

OffersReq offersReq2FromJson(String str) =>
    OffersReq.fromJson(json.decode(str));

String offersReq2ToJson(OffersReq data) => json.encode(data.toJson());

class OffersReq {
  final num? mvtItemId;
  final String? mvtItemName;
  final num? optionGroupId;
  final String? optionGroupName;
  final String? optionName;
  final num? optionId;
  final num? offerPrice;
  final num? customerId;
  final num? vendorId;
  final num? prchOrdrId;
  final String? statusCd;
  final num? variant1OptionId;
  final String? variant1OptionName;
  final num? variant2OptionId;
  final String? variant2OptionName;
  final num? variant3OptionId;
  final String? variant3OptionName;
  final num? variant1OptionGroupId;
  final String? variant1OptionGroupName;
  final num? variant2OptionGroupId;
  final String? variant2OptionGroupName;
  final num? variant3OptionGroupId;
  final String? variant3OptionGroupName;
  final String? remarks;
  // final String? variant_2_option_name;
  final num? quantity;
  List<Media>? medias;
  final List<String>? paths;
  final List<Uint8List>? bytes;
  final num? perUnitPrice;
  num? gst;

  OffersReq({
    //  required this.variant_2_option_name,
    this.mvtItemId,
    this.mvtItemName,
    this.optionGroupId,
    this.optionGroupName,
    this.optionName,
    this.optionId,
    this.offerPrice,
    this.customerId,
    this.vendorId,
    this.prchOrdrId,
    this.statusCd,
    this.variant1OptionId,
    this.variant1OptionName,
    this.variant2OptionId,
    this.variant2OptionName,
    this.variant3OptionId,
    this.variant3OptionName,
    this.variant1OptionGroupId,
    this.variant1OptionGroupName,
    this.variant2OptionGroupId,
    this.variant2OptionGroupName,
    this.variant3OptionGroupId,
    this.variant3OptionGroupName,
    this.remarks,
    this.quantity,
    this.medias,
    this.paths,
    this.bytes,
    this.perUnitPrice,
    this.gst,
  });

  factory OffersReq.fromJson(Map<String, dynamic> json) => OffersReq(
        mvtItemId: json["mvt_item_id"],
        // variant_2_option_name: json["variant_2_option_name"],
        mvtItemName: json["mvt_item_name"],
        optionGroupId: json["option_group_id"],
        optionGroupName: json["option_group_name"],
        optionName: json["option_name"],
        optionId: json["option_id"],
        offerPrice: json["offer_price"],
        customerId: json["customer_id"],
        vendorId: json["vendor_id"],
        prchOrdrId: json["prch_ordr_id"],
        statusCd: json["status_cd"],
        variant1OptionId: json["variant1OptionId"],
        variant1OptionName: json["variant1OptionName"],
        variant2OptionId: json["variant2OptionId"],
        variant2OptionName: json["variant2OptionName"],
        variant3OptionId: json["variant3OptionId"],
        variant3OptionName: json["variant3OptionName"],
        variant1OptionGroupId: json["variant1OptionGroupId"],
        variant1OptionGroupName: json["variant1OptionGroupName"],
        variant2OptionGroupId: json["variant2OptionGroupId"],
        variant2OptionGroupName: json["variant2OptionGroupName"],
        variant3OptionGroupId: json["variant3OptionGroupId"],
        variant3OptionGroupName: json["variant3OptionGroupName"],
        remarks: json["remarks"],
        quantity: json["quantity"],
        medias: json["medias"] == null
            ? []
            : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        // "variant_2_option_name": variant_2_option_name,
        "mvt_item_id": mvtItemId,
        "mvt_item_name": mvtItemName,
        "option_group_id": optionGroupId,
        "option_group_name": optionGroupName,
        "option_name": optionName,
        "option_id": optionId,
        "offer_price": offerPrice,
        "customer_id": customerId,
        "vendor_id": vendorId,
        "prch_ordr_id": prchOrdrId,
        "status_cd": statusCd,
        "variant1OptionId": variant1OptionId,
        "variant1OptionName": variant1OptionName,
        "variant2OptionId": variant2OptionId,
        "variant2OptionName": variant2OptionName,
        "variant3OptionId": variant3OptionId,
        "variant3OptionName": variant3OptionName,
        "variant1OptionGroupId": variant1OptionGroupId,
        "variant1OptionGroupName": variant1OptionGroupName,
        "variant2OptionGroupId": variant2OptionGroupId,
        "variant2OptionGroupName": variant2OptionGroupName,
        "variant3OptionGroupId": variant3OptionGroupId,
        "variant3OptionGroupName": variant3OptionGroupName,
        "remarks": remarks,
        "quantity": quantity,
        "per_unit_price": perUnitPrice,
        "gst": gst,
        "medias": medias == null
            ? []
            : List<dynamic>.from(medias!.map((x) => x.toJson())),
      };
}

class Media {
  String? title;
  String? url;
  String? previewUrl;

  Media({
    this.title,
    this.url,
    this.previewUrl,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        title: json["title"],
        url: json["url"],
        previewUrl: json["previewUrl"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "url": url,
        "previewUrl": previewUrl,
      };
}
