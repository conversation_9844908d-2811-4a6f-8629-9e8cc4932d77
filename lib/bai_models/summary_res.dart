// // To parse this JSON data, do
// //
// //     final summaryResponse = summaryResponseFromJson(jsonString);

// import 'dart:convert';

// List<SummaryResponse> summaryResponseFromJson(String str) =>
//     List<SummaryResponse>.from(
//         json.decode(str).map((x) => SummaryResponse.fromJson(x)));

// String summaryResponseToJson(List<SummaryResponse> data) =>
//     json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

// class SummaryResponse {
//   final List<VendorGroupedProduct>? vendorGroupedProducts;
//   final Summary? summary;

//   SummaryResponse({
//     this.vendorGroupedProducts,
//     this.summary,
//   });

//   SummaryResponse copyWith({
//     List<VendorGroupedProduct>? vendorGroupedProducts,
//     Summary? summary,
//   }) =>
//       SummaryResponse(
//         vendorGroupedProducts:
//             vendorGroupedProducts ?? this.vendorGroupedProducts,
//         summary: summary ?? this.summary,
//       );

//   factory SummaryResponse.fromJson(Map<String, dynamic> json) =>
//       SummaryResponse(
//         vendorGroupedProducts: json["vendorGroupedProducts"] == null
//             ? []
//             : List<VendorGroupedProduct>.from(json["vendorGroupedProducts"]!
//                 .map((x) => VendorGroupedProduct.fromJson(x))),
//         summary:
//             json["summary"] == null ? null : Summary.fromJson(json["summary"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "vendorGroupedProducts": vendorGroupedProducts == null
//             ? []
//             : List<dynamic>.from(vendorGroupedProducts!.map((x) => x.toJson())),
//         "summary": summary?.toJson(),
//       };
// }

// class Summary {
//   final String? vendorName;
//   final String? vendorPhone;
//   final num? subtotal;
//   final num? discount;
//   final num? transportationCharge;
//   final num? gst;
//   final num? total;
//   final String? brand;
//   final num? siteId;
//   final String? siteName;
//   final String? mrNo;
//   final DateTime? mrDate;
//   final DateTime? quoteDate;
//   final String? categoryName;
//   final num? summaryDiscount;
//   final num? summaryTransportationCharges;
//   final String? buyerName;
//   final num? cappCategoryId;

//   Summary({
//     this.vendorName,
//     this.subtotal,
//     this.discount,
//     this.transportationCharge,
//     this.gst,
//     this.total,
//     this.brand,
//     this.siteId,
//     this.siteName,
//     this.mrNo,
//     this.mrDate,
//     this.quoteDate,
//     this.categoryName,
//     this.summaryDiscount,
//     this.summaryTransportationCharges,
//     this.buyerName,
//     this.cappCategoryId,
//     this.vendorPhone,
//   });

//   Summary copyWith({
//     String? vendorName,
//     num? subtotal,
//     num? discount,
//     num? transportationCharge,
//     num? gst,
//     num? total,
//     String? brand,
//     num? siteId,
//     String? siteName,
//     String? mrNo,
//     DateTime? mrDate,
//     DateTime? quoteDate,
//     String? categoryName,
//     num? summaryDiscount,
//     num? summaryTransportationCharges,
//   }) =>
//       Summary(
//         vendorName: vendorName ?? this.vendorName,
//         subtotal: subtotal ?? this.subtotal,
//         discount: discount ?? this.discount,
//         transportationCharge: transportationCharge ?? this.transportationCharge,
//         gst: gst ?? this.gst,
//         total: total ?? this.total,
//         brand: brand ?? this.brand,
//         siteId: siteId ?? this.siteId,
//         siteName: siteName ?? this.siteName,
//         mrNo: mrNo ?? this.mrNo,
//         mrDate: mrDate ?? this.mrDate,
//         quoteDate: quoteDate ?? this.quoteDate,
//         categoryName: categoryName ?? this.categoryName,
//         summaryDiscount: summaryDiscount ?? this.summaryDiscount,
//         summaryTransportationCharges:
//             summaryTransportationCharges ?? this.summaryTransportationCharges,
//       );

//   factory Summary.fromJson(Map<String, dynamic> json) => Summary(
//         vendorName: json["vendorName"],
//         vendorPhone: json["vendorPhone"],
//         subtotal: json["subtotal"],
//         discount: json["discount"],
//         transportationCharge: json["transportationCharge"],
//         gst: json["gst"],
//         total: json["total"],
//         brand: json["brand"],
//         siteId: json["siteId"],
//         siteName: json["siteName"],
//         mrNo: json["mrNo"],
//         mrDate: json["mrDate"] == null ? null : DateTime.parse(json["mrDate"]),
//         quoteDate: (json["quoteDate"] == null ||
//                 json["quoteDate"].toString().trim().isEmpty ||
//                 json["quoteDate"].toString() == "null")
//             ? null
//             : DateTime.tryParse(json["quoteDate"].toString()),
//         categoryName: json["categoryName"],
//         summaryDiscount: json["summaryDiscount"],
//         summaryTransportationCharges: json["summaryTransportationCharges"],
//         buyerName: json["buyerName"],
//         cappCategoryId: json["cappCategoryId"],
//       );

//   Map<String, dynamic> toJson() => {
//         "vendorName": vendorName,
//         "subtotal": subtotal,
//         "discount": discount,
//         "transportationCharge": transportationCharge,
//         "gst": gst,
//         "total": total,
//         "brand": brand,
//         "siteId": siteId,
//         "siteName": siteName,
//         "mrNo": mrNo,
//         "mrDate": mrDate?.toIso8601String(),
//         "quoteDate": quoteDate?.toIso8601String(),
//         "categoryName": categoryName,
//         "summaryDiscount": summaryDiscount,
//         "summaryTransportationCharges": summaryTransportationCharges,
//       };
// }

// class VendorGroupedProduct {
//   final String? vendorName;
//   final List<MatchedProduct>? matchedProducts;

//   VendorGroupedProduct({
//     this.vendorName,
//     this.matchedProducts,
//   });

//   VendorGroupedProduct copyWith({
//     String? vendorName,
//     List<MatchedProduct>? matchedProducts,
//   }) =>
//       VendorGroupedProduct(
//         vendorName: vendorName ?? this.vendorName,
//         matchedProducts: matchedProducts ?? this.matchedProducts,
//       );

//   factory VendorGroupedProduct.fromJson(Map<String, dynamic> json) =>
//       VendorGroupedProduct(
//         vendorName: json["vendorName"],
//         matchedProducts: json["matchedProducts"] == null
//             ? []
//             : List<MatchedProduct>.from(json["matchedProducts"]!
//                 .map((x) => MatchedProduct.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "vendorName": vendorName,
//         "matchedProducts": matchedProducts == null
//             ? []
//             : List<dynamic>.from(matchedProducts!.map((x) => x.toJson())),
//       };
// }

// class MatchedProduct {
//   final String? productName;
//   final List<Detail>? details;

//   MatchedProduct({
//     this.productName,
//     this.details,
//   });

//   MatchedProduct copyWith({
//     String? productName,
//     List<Detail>? details,
//   }) =>
//       MatchedProduct(
//         productName: productName ?? this.productName,
//         details: details ?? this.details,
//       );

//   factory MatchedProduct.fromJson(Map<String, dynamic> json) => MatchedProduct(
//         productName: json["productName"],
//         details: json["details"] == null
//             ? []
//             : List<Detail>.from(
//                 json["details"]!.map((x) => Detail.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "productName": productName,
//         "details": details == null
//             ? []
//             : List<dynamic>.from(details!.map((x) => x.toJson())),
//       };
// }

// class Detail {
//   final num? prchOrdrOffrId;
//   final String? vendorName;
//   final num? prchOrdrId;
//   final String? mvtItemName;
//   final num? quantity;
//   final String? unit;
//   final num? perUnitPrice;
//   final num? offerPrice;
//   final num? gst;
//   final String? brand;
//   final String? statusCd;
//   final String? brand2;
//   final num? siteId;
//   final String? siteName;
//   final String? mrNo;
//   final DateTime? mrDate;
//   final DateTime? quoteDate;
//   final String? categoryName;
//   final num? discount;
//   final num? transportationCharges;
//   final bool? isAccepted;
//   final num? sellerId;
//   final String? quantityChangeYn;

//   Detail({
//     this.prchOrdrOffrId,
//     this.vendorName,
//     this.prchOrdrId,
//     this.mvtItemName,
//     this.quantity,
//     this.unit,
//     this.perUnitPrice,
//     this.offerPrice,
//     this.gst,
//     this.brand,
//     this.brand2,
//     this.siteId,
//     this.siteName,
//     this.mrNo,
//     this.mrDate,
//     this.quoteDate,
//     this.categoryName,
//     this.discount,
//     this.transportationCharges,
//     this.isAccepted,
//     this.sellerId,
//     this.statusCd,
//     this.quantityChangeYn,
//   });

//   Detail copyWith({
//     num? prchOrdrOffrId,
//     String? vendorName,
//     num? prchOrdrId,
//     String? mvtItemName,
//     num? quantity,
//     String? unit,
//     num? perUnitPrice,
//     num? offerPrice,
//     num? gst,
//     String? brand,
//     num? siteId,
//     String? siteName,
//     String? mrNo,
//     DateTime? mrDate,
//     DateTime? quoteDate,
//     String? categoryName,
//     num? discount,
//     num? transportationCharges,
//     String? quantityChangeYn,
//   }) =>
//       Detail(
//         prchOrdrOffrId: prchOrdrOffrId ?? this.prchOrdrOffrId,
//         vendorName: vendorName ?? this.vendorName,
//         prchOrdrId: prchOrdrId ?? this.prchOrdrId,
//         mvtItemName: mvtItemName ?? this.mvtItemName,
//         quantity: quantity ?? this.quantity,
//         unit: unit ?? this.unit,
//         perUnitPrice: perUnitPrice ?? this.perUnitPrice,
//         offerPrice: offerPrice ?? this.offerPrice,
//         gst: gst ?? this.gst,
//         brand: brand ?? this.brand,
//         siteId: siteId ?? this.siteId,
//         siteName: siteName ?? this.siteName,
//         mrNo: mrNo ?? this.mrNo,
//         mrDate: mrDate ?? this.mrDate,
//         quoteDate: quoteDate ?? this.quoteDate,
//         categoryName: categoryName ?? this.categoryName,
//         discount: discount ?? this.discount,
//         transportationCharges:
//             transportationCharges ?? this.transportationCharges,
//       );

//   factory Detail.fromJson(Map<String, dynamic> json) => Detail(
//         prchOrdrOffrId: json["prchOrdrOffrId"],
//         vendorName: json["vendorName"],
//         prchOrdrId: json["prchOrdrId"],
//         mvtItemName: json["mvtItemName"],
//         quantity: json["quantity"],
//         unit: json["unit"],
//         perUnitPrice: json["perUnitPrice"],
//         offerPrice: json["offerPrice"],
//         gst: json["gst"],
//         brand: json["brand"],
//         brand2: json["brand2"],
//         siteId: json["siteId"],
//         siteName: json["siteName"],
//         mrNo: json["mrNo"],
//         mrDate: json["mrDate"] == null ? null : DateTime.parse(json["mrDate"]),
//         quoteDate: (json["quoteDate"] == null ||
//                 json["quoteDate"].toString().trim().isEmpty ||
//                 json["quoteDate"].toString() == "null")
//             ? null
//             : DateTime.tryParse(json["quoteDate"].toString()),
//         categoryName: json["categoryName"],
//         discount: json["discount"],
//         transportationCharges: json["transportationCharges"],
//         isAccepted: json["isAccepted"],
//         sellerId: json["sellerId"],
//         statusCd: json["statusCd"],
//         quantityChangeYn: json["quantityChangeYN"],
//       );

//   Map<String, dynamic> toJson() => {
//         "prchOrdrOffrId": prchOrdrOffrId,
//         "vendorName": vendorName,
//         "prchOrdrId": prchOrdrId,
//         "mvtItemName": mvtItemName,
//         "quantity": quantity,
//         "unit": unit,
//         "perUnitPrice": perUnitPrice,
//         "offerPrice": offerPrice,
//         "gst": gst,
//         "brand": brand,
//         "siteId": siteId,
//         "siteName": siteName,
//         "mrNo": mrNo,
//         "mrDate": mrDate?.toIso8601String(),
//         "quoteDate": quoteDate?.toIso8601String(),
//         "categoryName": categoryName,
//         "discount": discount,
//         "transportationCharges": transportationCharges,
//         "statusCd": statusCd,
//         "quantityChangeYN": quantityChangeYn,
//       };
// }

import 'dart:convert';

class SummaryResponse {
  List<VendorGroupedProduct>? vendorGroupedProducts;
  Summary? summary;
  bool? isOfferApplied;

  SummaryResponse({
    this.vendorGroupedProducts,
    this.summary,
    this.isOfferApplied,
  });

  factory SummaryResponse.fromRawJson(String str) =>
      SummaryResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SummaryResponse.fromJson(Map<String, dynamic> json) =>
      SummaryResponse(
        vendorGroupedProducts: json["vendorGroupedProducts"] == null
            ? []
            : List<VendorGroupedProduct>.from(json["vendorGroupedProducts"]!
                .map((x) => VendorGroupedProduct.fromJson(x))),
        summary:
            json["summary"] == null ? null : Summary.fromJson(json["summary"]),
        isOfferApplied: json["isOfferApplied"],
      );

  Map<String, dynamic> toJson() => {
        "vendorGroupedProducts": vendorGroupedProducts == null
            ? []
            : List<dynamic>.from(vendorGroupedProducts!.map((x) => x.toJson())),
        "summary": summary?.toJson(),
        "isOfferApplied": isOfferApplied,
      };
}

class Summary {
  String? sellerName;
  String? vendorName;
  num? subtotal;
  num? discount;
  num? transportationCharge;
  num? gst;
  num? total;
  num? siteId;
  String? siteName;
  String? mrNo;
  DateTime? mrDate;
  DateTime? quoteDate;
  String? categoryName;
  num? summaryDiscount;
  num? summaryTransportationCharges;
  String? buyerName;
  num? cappCategoryId;
  String? sellerPhone;
  String? vendorPhone;
  String? buyerPhone;
  num? offerPrice;
  String? siteAccess;
  SiteAddress? siteAddress;
  String? adminEmail;
  String? vendorEmail;
  String? prchOrdrSplitStatus;
  String? showOfferYN;
  String? mrStatus;
  String? submitOfferYN;
  String? showSelectVendorButton;
  String? showLowestOfferYN;
  String? showInviteVendorButton;
  String? showNotInterestedButton;

  Summary({
    this.sellerName,
    this.vendorName,
    this.subtotal,
    this.discount,
    this.transportationCharge,
    this.gst,
    this.total,
    this.siteId,
    this.siteName,
    this.mrNo,
    this.mrDate,
    this.quoteDate,
    this.categoryName,
    this.summaryDiscount,
    this.summaryTransportationCharges,
    this.buyerName,
    this.cappCategoryId,
    this.sellerPhone,
    this.vendorPhone,
    this.buyerPhone,
    this.offerPrice,
    this.siteAccess,
    this.siteAddress,
    this.adminEmail,
    this.vendorEmail,
    this.prchOrdrSplitStatus,
    this.showOfferYN,
    this.mrStatus,
    this.submitOfferYN,
    this.showSelectVendorButton,
    this.showLowestOfferYN,
    this.showInviteVendorButton,
    this.showNotInterestedButton,
  });

  factory Summary.fromRawJson(String str) => Summary.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Summary.fromJson(Map<String, dynamic> json) => Summary(
        sellerName: json["sellerName"],
        vendorName: json["vendorName"],
        subtotal: json["subtotal"],
        discount: json["discount"],
        transportationCharge: json["transportationCharge"],
        gst: json["gst"],
        total: json["total"],
        siteId: json["siteId"],
        siteName: json["siteName"],
        prchOrdrSplitStatus: json["prchOrdrSplitStatus"],
        mrNo: json["mrNo"],
        mrDate: json["mrDate"] == null ? null : DateTime.parse(json["mrDate"]),
        quoteDate: json["quoteDate"] == null
            ? null
            : DateTime.parse(json["quoteDate"]),
        categoryName: json["categoryName"],
        summaryDiscount: json["summaryDiscount"],
        summaryTransportationCharges: json["summaryTransportationCharges"],
        buyerName: json["buyerName"],
        cappCategoryId: json["cappCategoryId"],
        sellerPhone: json["sellerPhone"],
        vendorPhone: json["vendorPhone"],
        buyerPhone: json["buyerPhone"],
        offerPrice: json["offerPrice"],
        siteAccess: json["siteAccess"],
        siteAddress: json["siteAddress"] == null
            ? null
            : SiteAddress.fromJson(json["siteAddress"]),
        adminEmail: json["adminEmail"],
        vendorEmail: json["vendorEmail"],
        showOfferYN: json["showOfferYN"],
        mrStatus: json["mrStatus"],
        submitOfferYN: json["submitOfferYN"],
        showSelectVendorButton: json["showSelectVendorButton"],
        showLowestOfferYN: json["showLowestOfferYN"],
          showInviteVendorButton: json["showInviteVendorButton"],
        showNotInterestedButton: json["showNotInterestedButton"],
      );

  Map<String, dynamic> toJson() => {
        "sellerName": sellerName,
        "subtotal": subtotal,
        "discount": discount,
        "prchOrdrSplitStatus": prchOrdrSplitStatus,
        "transportationCharge": transportationCharge,
        "gst": gst,
        "total": total,
        "siteId": siteId,
        "siteName": siteName,
        "mrNo": mrNo,
        "mrDate": mrDate?.toIso8601String(),
        "quoteDate": quoteDate,
        "categoryName": categoryName,
        "summaryDiscount": summaryDiscount,
        "summaryTransportationCharges": summaryTransportationCharges,
        "buyerName": buyerName,
        "cappCategoryId": cappCategoryId,
        "sellerPhone": sellerPhone,
        "siteAccess": siteAccess,
        "siteAddress": siteAddress?.toJson(),
        "adminEmail": adminEmail,
        "vendorEmail": vendorEmail,
        "showOfferYN": showOfferYN,
        "mrStatus": mrStatus,
        "submitOfferYN": submitOfferYN,
        "showSelectVendorButton": showSelectVendorButton,
        "showLowestOfferYN": showLowestOfferYN,
        "showInviteVendorButton": showInviteVendorButton,
        "showNotInterestedButton": showNotInterestedButton,
      };
}

class VendorGroupedProduct {
  String? sellerName;
  List<MatchedProduct>? matchedProducts;

  VendorGroupedProduct({
    this.sellerName,
    this.matchedProducts,
  });

  factory VendorGroupedProduct.fromRawJson(String str) =>
      VendorGroupedProduct.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VendorGroupedProduct.fromJson(Map<String, dynamic> json) =>
      VendorGroupedProduct(
        sellerName: json["sellerName"],
        matchedProducts: json["matchedProducts"] == null
            ? []
            : List<MatchedProduct>.from(json["matchedProducts"]!
                .map((x) => MatchedProduct.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "sellerName": sellerName,
        "matchedProducts": matchedProducts == null
            ? []
            : List<dynamic>.from(matchedProducts!.map((x) => x.toJson())),
      };
}

class MatchedProduct {
  String? productName;
  List<Detail>? details;

  MatchedProduct({
    this.productName,
    this.details,
  });

  factory MatchedProduct.fromRawJson(String str) =>
      MatchedProduct.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MatchedProduct.fromJson(Map<String, dynamic> json) => MatchedProduct(
        productName: json["productName"],
        details: json["details"] == null
            ? []
            : List<Detail>.from(
                json["details"]!.map((x) => Detail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "productName": productName,
        "details": details == null
            ? []
            : List<dynamic>.from(details!.map((x) => x.toJson())),
      };
}

class Detail {
  int? prchOrdrOffrId;
  num? sellerId;
  int? prchOrdrId;
  String? mvtItemName;
  int? quantity;
  String? quantityChangeYn;
  dynamic unit;
  dynamic totalGst;
  String? brand;
  int? projectId;
  String? siteName;
  String? mrNo;
  DateTime? mrDate;
  DateTime? quoteDate;
  String? categoryName;
  int? brandId;
  dynamic steelQuantity;
  String? brand2;
  int? cappCategoryId;
  String? buyerName;
  dynamic sellerPhone;
  String? statusCd;
  List<SubProduct>? subProducts;
  num? offerPrice;
  num? gst;
  bool? isAccepted;
  num? perUnitPrice;
  String? discardStatus;
  String? buyerNegoYN;
  String? sellerNegoYN;
  String? mrStatusButtonColor;

  Detail({
    this.prchOrdrOffrId,
    this.sellerId,
    this.prchOrdrId,
    this.mvtItemName,
    this.quantity,
    this.quantityChangeYn,
    this.unit,
    this.totalGst,
    this.brand,
    this.projectId,
    this.siteName,
    this.mrNo,
    this.mrDate,
    this.quoteDate,
    this.categoryName,
    this.brandId,
    this.steelQuantity,
    this.brand2,
    this.cappCategoryId,
    this.buyerName,
    this.sellerPhone,
    this.statusCd,
    this.subProducts,
    this.offerPrice,
    this.gst,
    this.isAccepted,
    this.perUnitPrice,
    this.discardStatus,
    this.buyerNegoYN,
    this.sellerNegoYN,
    this.mrStatusButtonColor,
  });

  factory Detail.fromRawJson(String str) => Detail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Detail.fromJson(Map<String, dynamic> json) => Detail(
        prchOrdrOffrId: json["prchOrdrOffrId"],
        sellerId: json["sellerId"],
        prchOrdrId: json["prchOrdrId"],
        mvtItemName: json["mvtItemName"],
        quantity: json["quantity"],
        quantityChangeYn: json["quantityChangeYN"],
        unit: json["unit"],
        totalGst: json["totalGST"],
        brand: json["brand"] != null
            ? (json["brand"] + "(${json["prchOrdrOffrId"]})")
            : null,
        projectId: json["projectId"],
        siteName: json["siteName"],
        mrNo: json["mrNo"],
        mrDate: json["mrDate"] == null ? null : DateTime.parse(json["mrDate"]),
        quoteDate: json["quoteDate"] == null
            ? null
            : DateTime.tryParse(json["quoteDate"].toString()),
        categoryName: json["categoryName"],
        brandId: json["brandId"],
        steelQuantity: json["steelQuantity"],
        brand2: json["brand2"],
        cappCategoryId: json["cappCategoryId"],
        buyerName: json["buyerName"],
        sellerPhone: json["sellerPhone"],
        statusCd: json["statusCd"],
        subProducts: json["subProducts"] == null
            ? []
            : List<SubProduct>.from(
                json["subProducts"]!.map((x) => SubProduct.fromJson(x))),
        offerPrice: json["offerPrice"],
        gst: json["gst"],
        isAccepted: json["isAccepted"],
        perUnitPrice: json["perUnitPrice"],
        discardStatus: json["discardStatus"],
        buyerNegoYN: json["buyerNegoYN"],
        sellerNegoYN: json["sellerNegoYN"],
        mrStatusButtonColor: json["mrStatusButtonColor"],
      );

  Map<String, dynamic> toJson() => {
        "prchOrdrOffrId": prchOrdrOffrId,
        "sellerId": sellerId,
        "prchOrdrId": prchOrdrId,
        "mvtItemName": mvtItemName,
        "quantity": quantity,
        "quantityChangeYN": quantityChangeYn,
        "unit": unit,
        "totalGST": totalGst,
        "brand": brand,
        "projectId": projectId,
        "siteName": siteName,
        "mrNo": mrNo,
        "mrDate": mrDate?.toIso8601String(),
        "quoteDate": quoteDate,
        "categoryName": categoryName,
        "brandId": brandId,
        "steelQuantity": steelQuantity,
        "brand2": brand2,
        "cappCategoryId": cappCategoryId,
        "buyerName": buyerName,
        "sellerPhone": sellerPhone,
        "statusCd": statusCd,
        "subProducts": subProducts == null
            ? []
            : List<dynamic>.from(subProducts!.map((x) => x.toJson())),
        "offerPrice": offerPrice,
        "gst": gst,
        "isAccepted": isAccepted,
        "perUnitPrice": perUnitPrice,
        "discardStatus": discardStatus,
        "buyerNegoYN": buyerNegoYN,
        "sellerNegoYN": sellerNegoYN,
        "mrStatusButtonColor": mrStatusButtonColor,
      };
}

class SubProduct {
  String? subProductName;
  String? subProductType;
  String? subProductQuantity;
  String? subProductUnit;
  num? subProductPricePerUnit;
  num? subProductTotalPrice;
  num? subProductOfferId;

  SubProduct({
    this.subProductName,
    this.subProductType,
    this.subProductQuantity,
    this.subProductUnit,
    this.subProductPricePerUnit,
    this.subProductTotalPrice,
    this.subProductOfferId,
  });

  factory SubProduct.fromRawJson(String str) =>
      SubProduct.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SubProduct.fromJson(Map<String, dynamic> json) => SubProduct(
        subProductName: json["subProductName"],
        subProductType: json["subProductType"],
        subProductQuantity: json["subProductQuantity"],
        subProductUnit: json["subProductUnit"],
        subProductPricePerUnit: json["subProductPricePerUnit"],
        subProductTotalPrice: json["subProductTotalPrice"],
        subProductOfferId: json["subProductOfferId"],
      );

  Map<String, dynamic> toJson() => {
        "subProductName": subProductName,
        "subProductType": subProductType,
        "subProductQuantity": subProductQuantity,
        "subProductUnit": subProductUnit,
        "subProductPricePerUnit": subProductPricePerUnit,
        "subProductTotalPrice": subProductTotalPrice,
        "subProductOfferId": subProductOfferId,
      };
}

class SiteAddress {
  final int? id;
  final String? sellingAddressLine1;
  final String? sellingAddressLine2;
  final String? city;
  final String? country;
  final String? state;
  final String? pincode;
  final double? latitude;
  final double? longitude;
  final String? cityLan;
  final String? cityLong;
  final DateTime? createdAt;

  SiteAddress({
    this.id,
    this.sellingAddressLine1,
    this.sellingAddressLine2,
    this.city,
    this.country,
    this.state,
    this.pincode,
    this.latitude,
    this.longitude,
    this.cityLan,
    this.cityLong,
    this.createdAt,
  });

  factory SiteAddress.fromJson(Map<String, dynamic> json) {
    return SiteAddress(
      id: json['id'] as int?,
      sellingAddressLine1: json['sellingAddressLine1'] as String?,
      sellingAddressLine2: json['sellingAddressLine2'] as String?,
      city: json['city'] as String?,
      country: json['country'] as String?,
      state: json['state'] as String?,
      pincode: json['pincode'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      cityLan: json['cityLan'] as String?,
      cityLong: json['cityLong'] as String?,
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sellingAddressLine1': sellingAddressLine1,
      'sellingAddressLine2': sellingAddressLine2,
      'city': city,
      'country': country,
      'state': state,
      'pincode': pincode,
      'latitude': latitude,
      'longitude': longitude,
      'cityLan': cityLan,
      'cityLong': cityLong,
      'createdAt': createdAt?.toIso8601String(),
    };
  }

  String getFormattedAddress() {
    List<String> addressParts = [];

    if (sellingAddressLine1 != null && sellingAddressLine1!.isNotEmpty) {
      addressParts.add(sellingAddressLine1!);
    }
    if (sellingAddressLine2 != null && sellingAddressLine2!.isNotEmpty) {
      addressParts.add(sellingAddressLine2!);
    }
    if (city != null && city!.isNotEmpty) {
      addressParts.add(city!);
    }
    if (state != null && state!.isNotEmpty) {
      addressParts.add(state!);
    }
    if (country != null && country!.isNotEmpty) {
      addressParts.add(country!);
    }
    if (pincode != null && pincode!.isNotEmpty) {
      addressParts.add(pincode!);
    }

    return addressParts.join(', ');
  }
}
