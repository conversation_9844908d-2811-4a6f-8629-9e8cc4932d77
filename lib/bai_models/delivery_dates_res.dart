// import 'dart:convert';

// class DeliveryDatesRes {
//   Data? data;
//   int? status;
//   String? statusDescription;

//   DeliveryDatesRes({
//     this.data,
//     this.status,
//     this.statusDescription,
//   });

//   factory DeliveryDatesRes.fromRawJson(String str) =>
//       DeliveryDatesRes.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory DeliveryDatesRes.fromJson(Map<String, dynamic> json) =>
//       DeliveryDatesRes(
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//         status: json["status"],
//         statusDescription: json["status_description"],
//       );

//   Map<String, dynamic> toJson() => {
//         "data": data?.toJson(),
//         "status": status,
//         "status_description": statusDescription,
//       };
// }

// class Data {
//   String? projectName;
//   num? projectId;
//   List<DeliveryDate>? deliveryDates;
//   bool? showPopUp;

//   Data({
//     this.projectName,
//     this.projectId,
//     this.deliveryDates,
//     this.showPopUp,
//   });

//   factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         projectName: json["projectName"],
//         projectId: json["projectId"],
//         deliveryDates: json["deliveryDates"] == null
//             ? []
//             : List<DeliveryDate>.from(
//                 json["deliveryDates"]!.map((x) => DeliveryDate.fromJson(x))),
//         showPopUp: json["showPopUp"],
//       );

//   Map<String, dynamic> toJson() => {
//         "projectName": projectName,
//         "projectId": projectId,
//         "deliveryDates": deliveryDates == null
//             ? []
//             : List<dynamic>.from(deliveryDates!.map((x) => x.toJson())),
//         "showPopUp": showPopUp,
//       };
// }

// class DeliveryDate {
//   DateTime? deliveryDate;
//   num? mrCount;
//   String? categoryName;
//   num? categoryId;

//   DeliveryDate({
//     this.deliveryDate,
//     this.mrCount,
//     this.categoryName,
//     this.categoryId,
//   });

//   factory DeliveryDate.fromRawJson(String str) =>
//       DeliveryDate.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory DeliveryDate.fromJson(Map<String, dynamic> json) => DeliveryDate(
//         deliveryDate: json["deliveryDate"] == null
//             ? null
//             : DateTime.parse(json["deliveryDate"]),
//         mrCount: json["mrCount"],
//         categoryName: json["categoryName"],
//         categoryId: json["categoryId"],
//       );

//   Map<String, dynamic> toJson() => {
//         "deliveryDate": deliveryDate?.toIso8601String(),
//         "mrCount": mrCount,
//         "categoryName": categoryName,
//         "categoryId": categoryId,
//       };
// }

import 'dart:convert';

class DeliveryDatesRes {
  Data? data;
  int? status;
  String? statusDescription;

  DeliveryDatesRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory DeliveryDatesRes.fromRawJson(String str) =>
      DeliveryDatesRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DeliveryDatesRes.fromJson(Map<String, dynamic> json) =>
      DeliveryDatesRes(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  String? projectName;
  int? projectId;
  List<DeliveryDate>? deliveryDates;
  bool? showPopUp;

  Data({
    this.projectName,
    this.projectId,
    this.deliveryDates,
    this.showPopUp,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        projectName: json["projectName"],
        projectId: json["projectId"],
        deliveryDates: json["deliveryDates"] == null
            ? []
            : List<DeliveryDate>.from(
                json["deliveryDates"]!.map((x) => DeliveryDate.fromJson(x))),
        showPopUp: json["showPopUp"],
      );

  Map<String, dynamic> toJson() => {
        "projectName": projectName,
        "projectId": projectId,
        "deliveryDates": deliveryDates == null
            ? []
            : List<dynamic>.from(deliveryDates!.map((x) => x.toJson())),
        "showPopUp": showPopUp,
      };
}

class DeliveryDate {
  DateTime? deliveryDate;
  int? mrCount;
  int? offerCount;
  String? splitName;
  int? splitId;
  int? prchOrdrId;
  String? splitStatus;
  bool? steelMr;

  DeliveryDate({
    this.prchOrdrId,
    this.deliveryDate,
    this.mrCount,
    this.offerCount,
    this.splitName,
    this.splitId,
    this.splitStatus,
    this.steelMr,
  });

  factory DeliveryDate.fromRawJson(String str) =>
      DeliveryDate.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DeliveryDate.fromJson(Map<String, dynamic> json) => DeliveryDate(
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        mrCount: json["mrCount"],
        offerCount: json["offerCount"],
        splitName: json["splitName"],
        splitId: json["splitId"],
        splitStatus: json["splitStatus"],
        steelMr: json["steelMr"],
        prchOrdrId: json["prchOrdrId"],
      );

  Map<String, dynamic> toJson() => {
        "deliveryDate": deliveryDate?.toIso8601String(),
        "mrCount": mrCount,
        "offerCount": offerCount,
        "splitName": splitName,
        "splitId": splitId,
        "splitStatus": splitStatus,
        "steelMr": steelMr,
        "prchOrdrId": prchOrdrId,
      };
}
