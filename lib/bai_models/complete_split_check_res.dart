class CompleteSplitCheckRes {
  Data? data;
  int? status;
  String? statusDescription;

  CompleteSplitCheckRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  CompleteSplitCheckRes.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    status = json['status'];
    statusDescription = json['status_description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status'] = status;
    data['status_description'] = statusDescription;
    return data;
  }
}

class Data {
  String? isSplitCompleted;

  Data({this.isSplitCompleted});

  Data.fromJson(Map<String, dynamic> json) {
    isSplitCompleted = json['isSplitCompleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['isSplitCompleted'] = isSplitCompleted;
    return data;
  }
}
