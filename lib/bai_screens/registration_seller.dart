import 'dart:ui';

import 'package:connectone/bai_blocs/seller_reg/cubit/seller_registration_cubit.dart';
import 'package:connectone/bai_models/pricing_res.dart';
import 'package:connectone/bai_models/register_req.dart';
import 'package:connectone/bai_screens/registration_buyer.dart';
import 'package:connectone/bai_screens/registration_otp.dart';
import 'package:connectone/bai_screens/select_services.dart';
import 'package:connectone/bai_screens/vendor_groups_screen.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../bai_models/vendors_res.dart';
import '../core/bai_widgets/help_info.dart';

class RegistrationSellerScreen extends StatefulWidget {
  const RegistrationSellerScreen({Key? key}) : super(key: key);

  @override
  _RegistrationSellerScreenState createState() =>
      _RegistrationSellerScreenState();
}

class _RegistrationSellerScreenState extends State<RegistrationSellerScreen> {
  final _formKey = GlobalKey<FormState>();
  final List<String> _selectedAreas = [];
  final List<AreaOfSupply> _selectedAreasOfSupply = [];

  final bool _showBaiMember = false;
  bool _showCentres = false;
  bool _showManufacturer = false;
  bool _showdesignation = false;
  bool _showCompanyName = false;
  bool _showGSTIN = false;
  bool _showAreaOfSupply1 = false;
  bool _showAreaOfSupply2 = false;
  bool _showCategory1 = false;
  bool _showCategory2 = false;
  Vendor? selectedValue01;

  String? _selectedDistrict;
  var _selectedDistrictId = "";
  var _selectedVendorId = "";
  String? _selectedDesignation;
  String _selectedDesignationId = "";
  String? _selectedBusiness;
  var _selectedBusinessId = "";
  String? _selectedGst;
  var _selectedGstId = "";
  String? _baiMember;
  String? _selectedManufacturer;
  final String _selectedManufacturerId = "";

  String? _selectedGSTType;
  String? _selectedState;

  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _gstinController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _websiteController = TextEditingController();
  final TextEditingController _pwController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _sellerController = TextEditingController();
  final TextEditingController _businessEmailController =
      TextEditingController();
  final TextEditingController _businessPhoneController =
      TextEditingController();

  final ValueNotifier<List<String>> selectedGroupsNotifier =
      ValueNotifier<List<String>>([]);
  int queryLength = 0;

  @override
  void initState() {
    super.initState();
    context.read<SellerRegistrationCubit>().loadData();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Seller Registration!\n\n1. Select if you are a BAI member\n\n2. Choose your BAI Centre/District\n\n3. Select your nature of business (Manufacturer/Dealer)\n\n4. Provide your business details and GST information\n\n5. Select your areas of supply and state\n\n6. Fill in your contact details and create login credentials\n\nComplete all required fields marked with * to proceed.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  final ScrollController _scrollController = ScrollController();

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  List<String> groups = [];
  List<String> selectedGroups = [];

  setGroups(List<PricingRes> pricing) {
    setState(() {
      groups = pricing.map((e) => e.group.toString()).toSet().toList();
    });
  }

  @override

  /// Builds the registration form for a seller.
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: AppBar(
        title: const Text('Registration - Seller'),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<SellerRegistrationCubit, SellerRegistrationState>(
          listener: (context, state) {
            if (state is SellerRegistrationLoaded) {
              setGroups(state.pricing ?? []);
            }
            if (state is SellerRegistrationError) {
              if (state.message.contains("already registered")) {
                Get.off(RegistrationOTP(
                  user: _emailController.text,
                  isSeller: true,
                  sendOtpInitially: true,
                ));
              } else {
                properAlert(state.message);
              }
            }
            if (state is SellerRegistrationSuccess) {
              Get.off(RegistrationOTP(
                user: _emailController.text ?? "",
                isSeller: true,
                sendOtpInitially: false,
              ));
            }
          },
          builder: (context, state) {
            (state is SellerRegistrationLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            if (state is SellerRegistrationLoaded) {
              return SizedBox(
                height: MediaQuery.of(context).size.height - 72,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      controller: _scrollController,
                      children: <Widget>[
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 20),
                            const Text(
                              'Are you a BAI member?',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: const Text('Yes',
                                        style: TextStyle(color: Colors.white)),
                                    value: 'Yes',
                                    groupValue: _baiMember,
                                    fillColor: const WidgetStatePropertyAll(
                                        Colors.white),
                                    onChanged: (value) {
                                      setState(() {
                                        clearCompanyField();
                                        _selectedDesignation = null;
                                        _showCentres = true;
                                        _baiMember = value;
                                      });
                                    },
                                  ),
                                ),
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: const Text('No',
                                        style: TextStyle(color: Colors.white)),
                                    value: 'No',
                                    groupValue: _baiMember,
                                    fillColor: const WidgetStatePropertyAll(
                                        Colors.white),
                                    onChanged: (value) {
                                      setState(() {
                                        clearCompanyField();
                                        _selectedDesignation = null;
                                        _showCentres = true;
                                        _baiMember = value;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        if (_showCentres) const SizedBox(height: 12),
                        if (_showCentres)
                          RegistrationDropdown(
                            labelText:
                                isBaiMember() ? "bai Centres" : "Districts",
                            items: isBaiMember()
                                ? state.baiCentres!.districts!
                                    .map((e) => e.name.toString())
                                    .toList()
                                : state.baiDistricts!.districts!
                                    .map((e) => e.name.toString())
                                    .toList(),
                            selectedValue: _selectedDistrict,
                            onChanged: (value) {
                              setState(() {
                                _selectedDistrict = value;
                                if (isBaiMember()) {
                                  _selectedDistrictId = state
                                      .baiCentres!.districts!
                                      .firstWhere((element) =>
                                          element.name == _selectedDistrict)
                                      .id
                                      .toString();
                                } else {
                                  _selectedDistrictId = state
                                      .baiDistricts!.districts!
                                      .firstWhere((element) =>
                                          element.name == _selectedDistrict)
                                      .id
                                      .toString();
                                }
                                _showManufacturer = true;
                                // context.read<SellerRegistrationCubit>().getRoles(_selectedVendorId??"0",'MBMR');
                                context
                                    .read<SellerRegistrationCubit>()
                                    .getVendors(_selectedDistrictId);
                              });
                            },
                          ),

                        // selectedValue: _selectedDesignation,
                        // items: state.roles!.map((e) => e.name.toString()).toList(),
                        // onChanged: (value) {
                        //   setState(() {
                        //     _selectedDesignation = value;
                        //     _selectedDesignationId = state.roles!.firstWhere((element) => element.name == value).id.toString();
                        //     if (_selectedDesignation == "Admin") {
                        //       _showNatures = true;
                        //       _showGst = true;
                        //       _showAddress = false;
                        //       _showNext1 = false;
                        //       _showNext2 = false;
                        //     } else {
                        //       _showNatures = false;
                        //       _showGst = false;
                        //       _showAddress = false;
                        //       _showNext1 = true;
                        //       _showNext2 = true;
                        //     }

                        // if (_selectedDesignation == "Admin") {
                        //   _showGst = true;
                        //   _showAddress = false;
                        //   _showNext1 = false;
                        //   _showNext2 = false;
                        // } else {
                        //   _showGst = false;
                        //   _showAddress = false;
                        //   _showNext1 = true;
                        //   _showNext2 = true;
                        // }
                        //       });
                        //     },
                        //   ),
                        // ],
                        // ),
                        if (_showManufacturer)
                          Column(
                            children: [
                              const SizedBox(height: 20),
                              RegistrationDropdown(
                                labelText: "Nature of Business",
                                items: state.natures!.natureOfBusiness!
                                    .map((e) => e.name.toString())
                                    .toList(),
                                selectedValue: _selectedManufacturer,
                                onChanged: (value) async {
                                  await NetworkController().getVendors1(
                                      _selectedDistrictId, '', 'MBMR');

                                  setState(() {
                                    _selectedManufacturer = value;
                                    _showCompanyName = true;
                                    if (value == "Manufacturer") {}
                                  });
                                  if (!isBaiMember()) {
                                    context
                                        .read<SellerRegistrationCubit>()
                                        .getRoles("0", 'MBMR');
                                  }
                                },
                              )
                            ],
                          ),
                        // if (_showCompanyName)
                        //   Column(
                        //     children: [
                        //       const SizedBox(height: 20),
                        //       isBaiMember()
                        //           ? RegistrationDropdown(
                        //               labelText: "Business Name",
                        //               items: state.vendors!.vendors!.map((e) => e.name.toString()).toList(),
                        //               selectedValue: _selectedBusiness,
                        //               onChanged: (value) {
                        //                 setState(() {
                        //                   _showGSTIN = value!.isNotEmpty;
                        //                   _showAreaOfSupply1 = value.isNotEmpty;
                        //                   _selectedBusiness = value;
                        //                   _selectedGst = state.vendors!.vendors!.firstWhere((element) => element.name == _selectedBusiness).gstNo;
                        //                   _gstinController.text = _selectedGst!;
                        //                   _selectedBusinessId =
                        //                       state.vendors!.vendors!.firstWhere((element) => element.name == _selectedBusiness).id.toString();
                        //                 });
                        //               },
                        //             )
                        //           : RegistrationTextField(
                        //               labelText: "Business Name",
                        //               controller: _companyNameController,
                        //               onChanged: (value) {
                        //                 setState(() {});
                        //                 setState(() {
                        //                   _showGSTIN = true;
                        //                   // _showAreaOfSupply = value.isNotEmpty;
                        //                   _selectedBusiness = value;
                        //                 });
                        //               },
                        //             ),
                        //     ],
                        //   ),
                        if (_showCompanyName) const SizedBox(height: 15),
                        if (_showCompanyName)
                          isBaiMember()
                              ? DropdownSearch<Vendor>(
                                  popupProps: PopupProps.menu(
                                    showSearchBox: true,
                                    itemBuilder: (context, item, isSelected) {
                                      print(item.name.toString());
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 20, vertical: 10),
                                        child: Text(item.name ?? "N/A",
                                            style: TextStyle(
                                                color: AppColors.primaryColor,
                                                fontWeight: FontWeight.bold)),
                                      );
                                    },
                                  ),
                                  items: state.vendors!.vendors!,
                                  filterFn: (item, filter) {
                                    return item.name
                                            ?.toLowerCase()
                                            .contains(filter) ??
                                        false;
                                  },
                                  dropdownDecoratorProps:
                                      DropDownDecoratorProps(
                                    dropdownSearchDecoration: InputDecoration(
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(4.0),
                                          borderSide: const BorderSide(
                                              color: Colors.white,
                                              width:
                                                  1.0), // Change the border color here
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(4.0),
                                          borderSide: const BorderSide(
                                              color: Colors.white,
                                              width:
                                                  1.0), // Change the focused border color here
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(4.0),
                                          borderSide: const BorderSide(
                                              color: Colors.white, width: 1.0),
                                        ),
                                        isDense: true,
                                        labelText: "Members",
                                        // hintText: "country in menu mode",
                                        labelStyle: const TextStyle(
                                            color: AppColors.white)),
                                  ),
                                  dropdownBuilder: (context, item) {
                                    print(item?.name.toString());
                                    return Text(
                                      item?.name ?? "N/A",
                                      style:
                                          const TextStyle(color: Colors.white),
                                    );
                                  },
                                  onChanged: (item) {
                                    selectedValue01 = item;
                                    setState(() {
                                      _selectedDesignation = null;
                                      _showdesignation = true;
                                      _selectedVendorId = item?.id ?? "";
                                      _showGSTIN = item!.name!.isNotEmpty;
                                      _showAreaOfSupply1 =
                                          item.name!.isNotEmpty;
                                      _selectedBusiness = item.name;
                                      _selectedGst = item.gstNo;

                                      _gstinController.text = _selectedGst!;
                                      _selectedBusinessId =
                                          item.id.toString() ?? '';
                                    });
                                    context
                                        .read<SellerRegistrationCubit>()
                                        .getRoles(
                                            _selectedVendorId ?? "0", 'MBMR');
                                  },
                                  selectedItem: selectedValue01,
                                )
                              : RegistrationTextField(
                                  labelText: "Business Name",
                                  controller: _companyNameController,
                                  onChanged: (value) {
                                    setState(() {});
                                    setState(() {
                                      // _showAreaOfSupply = value.isNotEmpty;
                                      _selectedBusiness = value;
                                    });
                                    print(_baiMember);
                                    setState(() {
                                      _showdesignation = true;
                                      _showGSTIN = true;
                                    });
                                  },
                                  onEditingComplete: () {
                                    // context
                                    //     .read<SellerRegistrationCubit>()
                                    //     .getRoles("0", 'MBMR');
                                    setState(() {
                                      _showdesignation = true;
                                      _showGSTIN = true;
                                    });
                                  },
                                ),
                        if (_showdesignation) const SizedBox(height: 20),
                        if (_showdesignation)
                          RegistrationDropdown(
                              labelText: 'Designation',
                              selectedValue: _selectedDesignation,

                              // items: ['1','2','3'],
                              // items: state.roles!.map((e) => e.name.toString()).toList(),
                              items: state.roles != null
                                  ? state.roles!
                                      .map((e) => e.name.toString())
                                      .toList()
                                  : [],
                              onChanged: (value) {
                                setState(() {
                                  _showManufacturer = true;

                                  _selectedDesignation = value;
                                  _selectedDesignationId = state.roles!
                                      .firstWhere(
                                          (element) => element.name == value)
                                      .id
                                      .toString();
                                  print('risaj---------$_selectedDesignation');
                                });
                              }),

                        if (_showGSTIN)
                          Column(
                            children: [
                              const SizedBox(height: 20),
                              RegistrationRichTextField(
                                labelText: 'GSTIN',
                                controller: _gstinController,
                                maxLength: 15,
                                capitalize: true,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'This field is required';
                                  }
                                  if (!RegExp(r'^\d{2}').hasMatch(value)) {
                                    return 'First two characters must be digits';
                                  }
                                  if (!RegExp(r'^[A-Za-z0-9]{15}')
                                      .hasMatch(value)) {
                                    return 'Characters from 3 to 15 must be digits or alphanumeric';
                                  }
                                  if (value.length != 15) {
                                    return 'GSTIN must be 15 characters long';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  setState(() {});
                                  setState(() {
                                    _showAreaOfSupply1 = true;
                                    _selectedGst = value;
                                    _selectedGstId = state.vendors!.vendors!
                                        .firstWhere((element) =>
                                            element.gstNo == _selectedGst)
                                        .id
                                        .toString();
                                  });
                                },
                              ),
                              const SizedBox(height: 20),
                              RegistrationDropdown(
                                labelText: "Type of GST Filing",
                                items: const ["Compound", "Regular"],
                                selectedValue: _selectedGSTType,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedGSTType = value;
                                    _showAreaOfSupply2 = true;
                                  });
                                },
                              ),
                            ],
                          ),
                        if (_showAreaOfSupply1 && _showAreaOfSupply2)
                          Container(
                            margin: const EdgeInsets.only(top: 20),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Column(
                              children: [
                                MultiSelectDropdown(
                                  labelText: 'Areas of Supply',
                                  items: state.areas!.areas!
                                      .map((e) => e.name.toString())
                                      .toList(),
                                  selectedValues: _selectedAreas,
                                  onChanged: (value) {
                                    setState(() {
                                      _showCategory1 = true;
                                    });
                                    _scrollToBottom();
                                  },
                                ),
                              ],
                            ),
                          ),
                        if (_showCategory1)
                          Column(
                            children: [
                              const SizedBox(height: 24),
                              RegistrationDropdown(
                                labelText: "State",
                                items: const ["Kerala"],
                                selectedValue: _selectedState,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedState = value;
                                    _showCategory2 = true;
                                  });
                                  _scrollToBottom();
                                },
                              ),
                            ],
                          ),
                        if (_showCategory1 && _showCategory2)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 24),
                              RegistrationRichTextField(
                                labelText: 'User Name',
                                controller: _nameController,
                                keyboardType: TextInputType.name,
                                onChanged: (value) {
                                  setState(() {});
                                },
                              ),
                              const SizedBox(height: 24),
                              RegistrationRichTextField(
                                labelText: 'Phone Number (Login ID)',
                                controller: _phoneController,
                                maxLength: 10,
                                keyboardType: TextInputType.phone,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'This field is required';
                                  }
                                  if (value.length != 10) {
                                    return 'Phone number must be 10 digits';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  setState(() {});
                                },
                              ),
                              const SizedBox(height: 24),
                              RegistrationPasswordField(
                                labelText: 'Password',
                                controller: _pwController,
                                onChanged: (value) {
                                  setState(() {});
                                },
                              ),
                              const SizedBox(height: 24),
                              RegistrationRichTextField(
                                labelText: 'Email',
                                controller: _emailController,
                                keyboardType: TextInputType.emailAddress,
                                onChanged: (value) {
                                  setState(() {});
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'This field is required';
                                  }
                                  if (!RegExp(
                                          r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                      .hasMatch(value)) {
                                    return 'Enter a valid email';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),
                              RegistrationTextField(
                                labelText: 'Website',
                                controller: _websiteController,
                                keyboardType: TextInputType.emailAddress,
                                onChanged: (value) {
                                  setState(() {});
                                },
                                validator: (value) {
                                  if (value != "") {
                                    const urlPattern =
                                        r'^(https?:\/\/)?(www\.)?([a-zA-Z0-9\-]+\.)+[a-zA-Z]{2,6}(\/\S*)?$';
                                    final result =
                                        RegExp(urlPattern).hasMatch(value!);
                                    if (!result) {
                                      return 'Please enter a valid website URL';
                                    }
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 20),
                              RegistrationRichTextField(
                                  labelText: 'Business Email',
                                  controller: _businessEmailController,
                                  keyboardType: TextInputType.emailAddress,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Email is required';
                                    }
                                    if (!GetUtils.isEmail(value)) {
                                      return 'Invalid email address';
                                    }
                                    return null;
                                  }),
                              const SizedBox(height: 20),
                              RegistrationTextField(
                                  labelText: 'Business Phone',
                                  controller: _businessPhoneController,
                                  keyboardType: TextInputType.phone,
                                  maxLength: 10,
                                  validator: (value) {
                                    if (value != '' && value!.length != 10) {
                                      return 'Phone number must be 10 digits';
                                    }
                                    return null;
                                  }),
                            ],
                          ),
                        if (_showCategory1 && _showCategory2)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 32),
                              (_selectedDesignation == "Admin")
                                  ?
                                  /////admin
                                  BaiButton(
                                      onTap: () {
                                        if (_formKey.currentState!.validate() &&
                                            _baiMember != null &&
                                            _selectedAreas.isNotEmpty) {
                                          if (_phoneController.text.length !=
                                              10) {
                                            alert(
                                                "Please enter a valid phone number.");
                                            return;
                                          }
                                          selectedCategories.clear();
                                          var nc = NetworkController();
                                          _selectedAreasOfSupply.clear();
                                          for (var i
                                              in state.areas?.areas ?? []) {
                                            if (_selectedAreas
                                                .contains(i.name)) {
                                              _selectedAreasOfSupply.add(
                                                  AreaOfSupply(
                                                      id: int.tryParse(
                                                          i.id ?? "")));
                                            }
                                          }
                                          var req = RegisterReq(
                                            city: _selectedDistrict,
                                            baiMember: isBaiMember(),
                                            natureOfBusiness:
                                                _selectedManufacturer,
                                            name: _nameController.text,
                                            vendorName: _selectedBusiness,
                                            vendorId: _selectedBusinessId,
                                            gstNumber: _selectedGst,
                                            addressLineOne: " ",
                                            addressLineTwo: " ",
                                            zip: "",
                                            location: "",
                                            customerType: "MBMR",
                                            // designation: "Admin",
                                            designation: _selectedDesignation,
                                            state: _selectedState,
                                            typeOfGstFilling: _selectedGSTType,
                                            areaOfSupply:
                                                _selectedAreasOfSupply,
                                            clientId: nc.organisationData
                                                ?.appClientAndroid,
                                            phone: _phoneController.text,
                                            email: _emailController.text,
                                            password: _pwController.text,
                                            website:
                                                _websiteController.text.trim(),
                                            // businessEmail:
                                            //     _businessEmailController.text
                                            //         .trim(),
                                            // businessPhone:
                                            //     _businessPhoneController.text
                                            //         .trim(),
                                            shippingAddress: ShippingAddress(
                                              addressLine1: " ",
                                              addressLine2: " ",
                                              city: "",
                                              state: "",
                                              pincode: "",
                                              country: "",
                                              latitude: 0,
                                              longitude: 0,
                                            ),
                                            countryCode: "+91",
                                            loginType: "PHONE",
                                            termsAndConditions: true,
                                          );
                                          Get.to(
                                            VendorGroupsScreen(
                                              pricing: state.pricing ?? [],
                                              isMember: isBaiMember(),
                                              isManufacturer: isManufacturer(),
                                              req: req,
                                            ),
                                          );
                                        } else {
                                          setState(() {});
                                        }
                                      },
                                      text: "Next > Select Category Groups",
                                      backgoundColor: Colors.white,
                                      textColor: AppColors.primaryColor,
                                    )
                                  : BaiButton(
                                      backgoundColor: Colors.white,
                                      textColor: AppColors.primaryColor,
                                      onTap: () {
                                        selectedCategories.clear();
                                        var nc = NetworkController();
                                        for (var i
                                            in state.areas?.areas ?? []) {
                                          if (_selectedAreas.contains(i.id)) {
                                            _selectedAreasOfSupply.add(
                                                AreaOfSupply(
                                                    id: int.tryParse(
                                                        i.id ?? "")));
                                          }
                                        }
                                        var req = RegisterReq(
                                          city: _selectedDistrict,
                                          baiMember: isBaiMember(),
                                          natureOfBusiness:
                                              _selectedManufacturer,
                                          name: _nameController.text,
                                          vendorName: _selectedBusiness,
                                          vendorId: _selectedBusinessId,
                                          gstNumber: _selectedGst,
                                          addressLineOne: " ",
                                          addressLineTwo: " ",
                                          zip: "",
                                          location: "",
                                          customerType: "MBMR",
                                          // designation: "Admin",
                                          designation: _selectedDesignation,
                                          state: _selectedState,
                                          typeOfGstFilling: _selectedGSTType,
                                          areaOfSupply: _selectedAreasOfSupply,
                                          clientId: nc.organisationData
                                              ?.appClientAndroid,
                                          phone: _phoneController.text,
                                          email: _emailController.text,
                                          password: _pwController.text,
                                          website:
                                              _websiteController.text.trim(),
                                          shippingAddress: ShippingAddress(
                                            addressLine1: " ",
                                            addressLine2: " ",
                                            city: "",
                                            state: "",
                                            pincode: "",
                                            country: "",
                                            latitude: 0,
                                            longitude: 0,
                                          ),
                                          countryCode: "+91",
                                          loginType: "PHONE",
                                          termsAndConditions: true,
                                        );
                                        context
                                            .read<SellerRegistrationCubit>()
                                            .registerSeller(req);
                                      },
                                      text: 'Register'),
                              if (_selectedAreas.isEmpty)
                                const Padding(
                                  padding: EdgeInsets.only(top: 20, bottom: 4),
                                  child: Center(
                                    child: Text(
                                      'Please select "Areas of supply"',
                                      style: TextStyle(color: Colors.red),
                                      textAlign: TextAlign.start,
                                    ),
                                  ),
                                ),
                              const SizedBox(height: 16),
                            ],
                          ),
                      ],
                    ),
                  ),
                ),
              );
            } else {
              return const SizedBox.shrink();
            }
          },
        ),
      ),
    );
  }

  bool isBaiMember() => _baiMember == 'Yes';
  bool isManufacturer() => _selectedManufacturer == 'Manufacturer';

  clearCompanyField() {
    _companyNameController.clear();
    _selectedBusiness = null;
    _selectedGst = null;
    _gstinController.clear();
  }
}

class GroupItem extends StatefulWidget {
  const GroupItem({
    Key? key,
    required this.title,
    required this.selectedGroupsNotifier,
    required this.onChanged,
  }) : super(key: key);

  final String title;
  final Function onChanged;
  final ValueNotifier<List<String>> selectedGroupsNotifier;

  @override
  State<GroupItem> createState() => _GroupItemState();
}

class _GroupItemState extends State<GroupItem> {
  bool isSelected = false;

  void _onCheckboxChanged(bool? val) {
    widget.onChanged();
    if (val == null) return;
    if (widget.selectedGroupsNotifier.value.length >= 2 && val == true) {
      // alert("You can select only 2 groups.");
      // return;
    }

    setState(() {
      if (val != isSelected) {
        isSelected = val;
        if (isSelected) {
          widget.selectedGroupsNotifier.value.add(widget.title);
        } else {
          widget.selectedGroupsNotifier.value.remove(widget.title);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Checkbox(
          value: isSelected,
          onChanged: _onCheckboxChanged,
          activeColor: AppColors.primaryColor,
        ),
        Expanded(
          child: Text(
            widget.title,
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

class RegistrationPasswordField extends StatefulWidget {
  final String labelText;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final FormFieldValidator<String>? validator;

  const RegistrationPasswordField({
    Key? key,
    required this.labelText,
    required this.controller,
    this.onChanged,
    this.validator,
  }) : super(key: key);

  @override
  State<RegistrationPasswordField> createState() =>
      _RegistrationPasswordFieldState();
}

class _RegistrationPasswordFieldState extends State<RegistrationPasswordField> {
  bool _isPasswordVisible = false;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      obscureText: !_isPasswordVisible,
      onChanged: widget.onChanged,
      keyboardType: TextInputType.visiblePassword,
      style: const TextStyle(color: Color.fromRGBO(255, 255, 255, 1)),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        label: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: widget.labelText,
                style: const TextStyle(color: Colors.white),
              ),
              const TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
        helperStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
        labelStyle: const TextStyle(color: Colors.white),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        isDense: true,
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: Colors.white,
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
      ),
      validator: widget.validator ??
          (value) {
            if (value == null || value.isEmpty) {
              return 'This field is required';
            }
            return null;
          },
    );
  }
}
