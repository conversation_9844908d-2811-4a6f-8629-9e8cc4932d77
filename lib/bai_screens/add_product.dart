import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:audio_session/audio_session.dart';
import 'package:connectone/bai_blocs/add_product/cubit/add_product_cubit.dart';
import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_models/insert_stock_req.dart';
import 'package:connectone/bai_models/item_offering_res.dart';
import 'package:connectone/bai_models/test_search_res.dart' as tsr;
import 'package:connectone/bai_screens/check_out.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/filter_multi_select.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:flutter_sound_platform_interface/flutter_sound_recorder_platform_interface.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';
import '../core/utils/app_routes.dart';
// import 'package:record/record.dart';

class AddProductPage extends StatefulWidget {
  const AddProductPage({
    Key? key,
    required this.item,
    this.isGeneral = false,
    this.orderGroupId,
    this.deliveryDate,
    this.category,
    required this.isMr,
    this.splitGroupId,
    this.categoryId,
    this.splitGroupName,
    this.isHide,
  }) : super(key: key);

  final tsr.Content item;
  final bool isGeneral;
  final bool? isHide;
  final int? orderGroupId;
  final DateTime? deliveryDate;
  final String? category;
  final bool isMr;
  final int? splitGroupId;
  final int? categoryId;
  final String? splitGroupName;

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  Codec _codec = Codec.aacMP4;
  final String _mPath = 'audio_${DateTime.now().millisecondsSinceEpoch}.mp4';
  FlutterSoundPlayer? _mPlayer = FlutterSoundPlayer();
  FlutterSoundRecorder? _mRecorder = FlutterSoundRecorder();
  var theSource = AudioSource.microphone;
  bool _mPlayerIsInited = false;
  bool _mRecorderIsInited = false;
  bool _mplaybackReady = false;

  final _formKey = GlobalKey<FormState>();

  final List<String> _images = [];
  final List<Uint8List> _imageBytes = [];
  final List<String> _files = [];
  final List<Uint8List> _fileBytes = [];
  final List<String> _audios = [];
  final List<Uint8List> _audioBytes = [];

  bool isAddedToCart = false;

  final TextEditingController _productNameController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _instructionsController = TextEditingController();

  String? _selectedUnit;
  String? _selectedUnitId;

  bool recording = false;

  final ImagePicker picker = ImagePicker();
  final ScrollController _scrollController = ScrollController();

  /// Scrolls the screen to the bottom.
  ///
  /// This function is used to scroll the screen to the bottom when the user
  /// submits a form or when the user clicks on the "Add to Cart" button.
  void _scrollToBottom() {
    /// Waits for 100 milliseconds before scrolling to the bottom.
    /// This is done to avoid scrolling to the bottom before the form is
    /// validated and the error messages are displayed.
    Future.delayed(const Duration(milliseconds: 100), () {
      /// Checks if the scroll controller has clients.
      /// If it does, it scrolls to the bottom of the screen.
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  bool isSteelReinforcement = false;

  pickFiles() async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
    } else {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
        allowMultiple:
            false, // Crucial for single file selection, avoids list issues
        withData: true, // Retrieve file bytes directly
      );

      if (result != null && result.files.isNotEmpty) {
        // Check for empty result
        PlatformFile file = result.files.first;

        // Use bytes for web. For non-web platforms, you *can* still
        // optionally use the path, but it's generally cleaner to use
        // the bytes for everything, especially if you need to do
        // anything like uploading the file.
        if (kIsWeb) {
          // Web: Use bytes
          if (file.bytes != null) {
            setState(() {
              _fileBytes.add(file.bytes!);
              _files.add(file.name); // Store filename for display, NOT the path
            });
          } else {
            //handle error of no bytes retrieved on web.
          }
        } else {
          // Non-web: Use bytes, can optionally use path if needed
          if (file.bytes != null) {
            setState(() {
              _fileBytes.add(file.bytes!);
              // Use path if you need it (for displaying, or local operations)
              _files.add(file.path ??
                  file.name); // Fallback to file name if path is somehow null
            });
          } else if (file.path != null) {
            //If not on web and no byte data, but we have a path (which should be the case)
            File selectedFile = File(file.path!);

            setState(() {
              //add the file.path to the files.
              _files.add(file.path!);

              //get bytes, add the bytes to _fileBytes
              selectedFile.readAsBytes().then((value) {
                _fileBytes.add(value);
              });
            });
          }
        }
      }
    }
  }

  bool hasAttachmentsMaxed() {
    return _images.length + _files.length + _audios.length >= 5;
  }

  @override
  void initState() {
    // NetworkController().getUnits();
    super.initState();
    context
        .read<AddEditProductCubit>()
        .getItemOffering(widget.item.id?.toInt().toString() ?? "");
    _mPlayer!.openPlayer().then((value) {
      setState(() {
        _mPlayerIsInited = true;
      });
    });

    openTheRecorder().then((value) {
      setState(() {
        _mRecorderIsInited = true;
      });
    });
    checkIfLimitExceeded();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to the Add Product screen!\n\nHere you can:\n\n1. Enter product details like name, quantity and unit\n\n2. Add images by taking photos or selecting from gallery\n\n3. Record voice notes for special instructions\n\n4. Attach documents for specifications\n\n5. Add written instructions\n\nAll fields marked with * are required. Fill in the details and click ADD PRODUCT when done.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  checkIfLimitExceeded() async {
    if (BaiCart.cartItems.length >= 10) {
      alert("You can only add 10 items at a time.");
      await Future.delayed(const Duration(seconds: 4)).then((value) {
        Navigator.pop(context);
      });
    }
  }

  /// Opens the recorder.
  ///
  /// This function requests microphone permission and configures the
  /// audio session to be able to record audio.
  ///
  /// If the permission is not granted, a [RecordingPermissionException] is thrown.
  ///
  /// If the encoder is not supported on the web, the encoder is changed to
  /// [Codec.opusWebM].
  ///
  /// The [AudioSessionConfiguration] is used to configure the audio session
  /// to be able to record audio.
  ///
  /// If the configuration is successful, the [_mRecorderIsInited] is set to true.
  Future<void> openTheRecorder() async {
    if (!kIsWeb) {
      var status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        throw RecordingPermissionException(
            'Microphone permission not granted.');
      }
    }
    await _mRecorder!.openRecorder();
    if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
      _codec = Codec.opusWebM;
      if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
        _mRecorderIsInited = true;
        return;
      }
    }
    final session = await AudioSession.instance;
    await session.configure(AudioSessionConfiguration(
      avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
      avAudioSessionCategoryOptions:
          AVAudioSessionCategoryOptions.allowBluetooth |
              AVAudioSessionCategoryOptions.defaultToSpeaker,
      avAudioSessionMode: AVAudioSessionMode.spokenAudio,
      avAudioSessionRouteSharingPolicy:
          AVAudioSessionRouteSharingPolicy.defaultPolicy,
      avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
      androidAudioAttributes: const AndroidAudioAttributes(
        contentType: AndroidAudioContentType.speech,
        flags: AndroidAudioFlags.none,
        usage: AndroidAudioUsage.voiceCommunication,
      ),
      androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      androidWillPauseWhenDucked: true,
    ));

    _mRecorderIsInited = true;
  }

  List<Variant> variants = [];

  Map<int, TextEditingController> controllers = {};

  /// Returns a list of selected values for the given option group id.
  ///
  /// This function iterates over the list of [Variant] objects and
  /// returns a list of strings that match the given option group id.
  ///
  /// The returned list will contain the [optionName] of the [Variant]
  /// objects that match the given option group id.
  List<String> getSelectedValues(int optionGroupId) {
    // Initialize an empty list to store the selected values.
    List<String> selecedValues = [];

    // Iterate over the list of Variant objects.
    for (var i in variants) {
      // Check if the option group id of the current variant matches
      // the given option group id.
      if (i.optionGroupId == optionGroupId) {
        // Add the option name of the current variant to the list.
        selecedValues.add(i.optionName ?? "");
      }
    }

    // Return the list of selected values.
    return selecedValues;
  }

  /// Shows a dialog to select the image source.
  ///
  /// This function shows an [AlertDialog] with two options to select the image
  /// source: "Take using Camera" and "Pick from Gallery". When the user selects
  /// an option, the dialog is closed and the [getImage] function is called with
  /// the selected option.
  void showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Select Image Source",
              style: TextStyle(fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                // Option to take the image using the camera.
                GestureDetector(
                  child: const Text("Take using Camera"),
                  onTap: () {
                    // Close the dialog.
                    Navigator.of(context).pop();
                    // Call the getImage function with the selected option.
                    getImage(fromCamera: true);
                  },
                ),
                const SizedBox(height: 16),
                // Option to pick the image from the gallery.
                GestureDetector(
                  child: const Text("Pick from Gallery"),
                  onTap: () {
                    // Close the dialog.
                    Navigator.of(context).pop();
                    // Call the getImage function with the selected option.
                    getImage(fromCamera: false);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Gets an image from the device's camera or gallery.
  ///
  /// This function requests an image from the device's camera or gallery
  /// and adds it to the list of images. The image is resized to 50% of its
  /// original quality to reduce the file size.
  ///
  /// If the device has reached the maximum number of allowed attachments,
  /// a message is shown to the user indicating that the maximum number of
  /// attachments has been reached.
  ///
  /// The [fromCamera] parameter specifies whether to request the image from
  /// the camera or the gallery. If [fromCamera] is true, the camera is used,
  /// otherwise the gallery is used.
  ///
  /// The function returns a [Future] that resolves when the image has been
  /// selected or the user has cancelled the operation.
  Future<void> getImage({bool fromCamera = true}) async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
    } else {
      try {
        final XFile? image = await picker.pickImage(
          source: fromCamera ? ImageSource.camera : ImageSource.gallery,
          requestFullMetadata: false,
          imageQuality: 25,
        );

        if (image != null) {
          final Uint8List imageBytes = await image.readAsBytes();

          if (mounted) {
            setState(() {
              _imageBytes.add(imageBytes);
              _images.add(image.path);
            });
          }

          if (kDebugMode) {
            print('Image captured: ${image.path}');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print(e);
        }
      }
    }
  }

  @override

  /// Builds the widget tree for the AddProduct screen.
  ///
  /// The screen displays a form with fields for the product name, quantity,
  /// unit, and instructions. It also displays a list of images and a button
  /// to add more images. The user can also record a voice note and add it to
  /// the request.
  ///
  /// The screen also displays a list of options and a button to add more options.
  /// The user can select the options and add them to the request.
  ///
  /// The screen also displays a list of files and a button to add more files.
  /// The user can select the files and add them to the request.
  ///
  /// The screen also displays a button to add the product to the material
  /// request list.
  ///
  /// The screen also displays a button to create a material request.
  ///
  /// The screen is used to add a product to the material request list.
  Widget build(BuildContext context) {
    print("AddProductPage: widget.isHide = ${widget.isHide}");
    var item = widget.item;
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.splitGroupName != null
            ? "Split - ${widget.splitGroupName}"
            : widget.orderGroupId != null
                ? "Split Material Request"
                : "Add ${widget.isMr ? "Material" : "Service"} Request"),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          Badge(
            label: Text(BaiCart.cartItems.length.toString()),
            offset: const Offset(-0, 6),
            child: IconButton(
              icon: const Icon(Icons.shopping_cart),
              onPressed: () {
                Get.to(CheckOut(
                  isMr: widget.isMr,
                  deliveryDate: widget.deliveryDate,
                ))?.then((val) {
                  setState(() {});
                }); // Navigates to the checkout screen
              },
            ),
          ),
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<AddEditProductCubit, AddEditProductState>(
          listener: (context, state) {
            if (state is AddProductLoaded) {
              if ((state.response?.data?.name ?? "")
                  .toLowerCase()
                  .contains("reinforcement")) {
                isSteelReinforcement = true;
              }
              var optionGroups = state.response?.data?.optionGroups ?? [];
              for (var group in optionGroups) {
                if (group.optionType == "VARI" && group.valueType == "TXTX") {
                  controllers[group.id?.toInt() ?? 0] = TextEditingController();
                }
              }
            }
          },
          builder: (context, state) {
            var border = const OutlineInputBorder(
              borderSide: BorderSide(
                color: Colors.black,
                width: 1,
              ),
            );
            (state is AddProductLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            if (state is AddProductLoaded) {
              var optionGroups = state.response?.data?.optionGroups ?? [];
              var unitOg = optionGroups.isNotEmpty
                  ? optionGroups.firstWhere(
                      (e) =>
                          e.name != null &&
                          e.name!.toLowerCase().contains("unit"),
                      orElse: () => OptionGroup(),
                    )
                  : null;

              List<OptionGroup> otherOptions = [];

              if (optionGroups.isNotEmpty) {
                for (var group in optionGroups) {
                  if (group.name == null ||
                      !group.name!.toLowerCase().contains("unit")) {
                    otherOptions.add(group);
                  }
                }
              }

              return SizedBox(
                  height: MediaQuery.of(context).size.height - 72,
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Form(
                      key: _formKey,
                      child: ListView(
                        controller: _scrollController,
                        children: [
                          if (!widget.isGeneral)
                            Text(
                              "${item.name}",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryColor,
                              ),
                            ),
                          //-LAST CHANGE
                          if (!widget.isGeneral) const SizedBox(height: 20),
                          if (widget.isGeneral && widget.isHide == true)
                            const SizedBox(height: 8),
                          if (widget.isGeneral && widget.isHide == true)
                            TextFormField(
                              controller: _productNameController,
                              decoration: InputDecoration(
                                labelText: "Product Name",
                                focusedBorder: border,
                                enabledBorder: border,
                                border: border,
                                labelStyle:
                                    const TextStyle(color: Colors.black),
                                isDense: true,
                              ),
                              keyboardType: TextInputType.text,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Product Name is required';
                                }
                                return null;
                              },
                            ),
                          if (widget.isGeneral && widget.isHide == true)
                            const SizedBox(height: 20),
                          if (!isSteelReinforcement && widget.isHide == true)
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _quantityController,
                                    decoration: InputDecoration(
                                      labelText: "Quantity",
                                      focusedBorder: border,
                                      enabledBorder: border,
                                      border: border,
                                      labelStyle:
                                          const TextStyle(color: Colors.black),
                                      // isDense: true,
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Quantity is required';
                                      }
                                      if (int.tryParse(value) == null) {
                                        return 'Enter a valid number';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                                if (unitOg != null && widget.isHide == true)
                                  const SizedBox(width: 10),
                                if (unitOg != null && widget.isHide == true)
                                  Expanded(
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedUnitId,
                                      decoration: InputDecoration(
                                        labelText: unitOg.name ?? "",
                                        focusedBorder: border,
                                        enabledBorder: border,
                                        border: border,
                                        labelStyle: const TextStyle(
                                            color: Colors.black),
                                        isDense: true,
                                      ),
                                      items: unitOg.options?.map((unit) {
                                        return DropdownMenuItem(
                                          value: unit.id.toString(),
                                          child: Text(unit.name ?? ""),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        var unitOp = unitOg.options?.firstWhere(
                                            (element) =>
                                                element.id.toString() == value,
                                            orElse: () => Option());
                                        if (unitOp != null) {
                                          setState(() {
                                            _selectedUnitId =
                                                unitOp.id.toString();
                                            _selectedUnit = unitOp.name;

                                            // Remove existing variants for the selected option group
                                            variants.removeWhere((variant) =>
                                                variant.optionGroupId ==
                                                unitOg.id?.toInt());

                                            // Add the new selected unit as a variant
                                            variants.add(Variant(
                                              optionGroupId:
                                                  unitOg.id?.toInt() ?? 0,
                                              optionGroupName:
                                                  unitOg.name ?? "",
                                              optionId: unitOp.id?.toInt() ?? 0,
                                              optionName: unitOp.name ?? "",
                                              offerCheckBoolean: false,
                                            ));
                                          });
                                        }
                                      },
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Unit is required';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                              ],
                            ),
                          if (otherOptions.isNotEmpty && !isSteelReinforcement)
                            const SizedBox(height: 20),
                          ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              var optionType = otherOptions[index].optionType;
                              var valueType = otherOptions[index].valueType;
                              if (optionType == "VARI") {
                                if (valueType == "SING") {
                                  return DropdownButtonFormField<String>(
                                    decoration: InputDecoration(
                                      isDense: true,
                                      focusedBorder: border,
                                      enabledBorder: border,
                                      border: border,
                                      labelText: otherOptions[index].name ?? "",
                                      labelStyle:
                                          const TextStyle(color: Colors.black),
                                    ),
                                    items: otherOptions[index]
                                        .options
                                        ?.map((Option value) {
                                      return DropdownMenuItem<String>(
                                        value: value.id.toString(),
                                        child: Text(value.name ?? ''),
                                      );
                                    }).toList(),
                                    onChanged: (String? newValue) {
                                      var itemOg = otherOptions[index];
                                      var itemOp = itemOg.options?.firstWhere(
                                          (element) =>
                                              element.id.toString() ==
                                              newValue);
                                      setState(() {
                                        for (var i in variants) {
                                          if (i.optionGroupId ==
                                              itemOp?.optionGroupId?.toInt()) {
                                            variants.remove(i);
                                          }
                                        }
                                        variants.add(Variant(
                                          optionGroupId:
                                              itemOg.id?.toInt() ?? 0,
                                          optionGroupName: itemOg.name ?? "",
                                          optionId: itemOp?.id?.toInt() ?? 0,
                                          optionName: itemOp?.name ?? "",
                                          offerCheckBoolean: true,
                                        ));
                                      });
                                    },
                                    hint: Text(otherOptions[index].name ?? ''),
                                  );
                                }
                                if (valueType == "MULT") {
                                  var item = otherOptions[index];
                                  if (isSteelReinforcement &&
                                      (item.name ?? "")
                                          .toLowerCase()
                                          .contains('size')) {
                                    return FilterMultiSelectNewSteel(
                                      items: item.options!
                                          .map((e) => e.name.toString())
                                          .toList(),
                                      optionGroup: unitOg,
                                      onChanged: (selectedValues) {
                                        var itemOg = otherOptions[index];
                                        var itemOgId = itemOg.id?.toInt() ?? 0;

                                        // Remove the existing variants for the current item group
                                        setState(() {
                                          variants.removeWhere((variant) =>
                                              variant.optionGroupId ==
                                              itemOgId);
                                        });

                                        // Add the new selected values to the variants
                                        List<Variant> newVariants =
                                            selectedValues.map((value) {
                                          String itemValue =
                                              value.split(' - ')[0];

                                          var option = item.options!.firstWhere(
                                              (element) =>
                                                  (element.name!) ==
                                                  (itemValue));
                                          return Variant(
                                            optionGroupId: itemOgId,
                                            optionGroupName: itemOg.name ?? "",
                                            optionId: option.id?.toInt() ?? 0,
                                            optionName: value,
                                            offerCheckBoolean: true,
                                          );
                                        }).toList();

                                        setState(() {
                                          variants.addAll(newVariants);
                                        });
                                      },
                                      labelText: item.name ?? 'N/A',
                                      selectedValues: getSelectedValues(
                                          item.id?.toInt() ?? 0),
                                    );
                                  } else {
                                    return FilterMultiSelectNew(
                                      items: item.options!
                                          .map((e) => e.name.toString())
                                          .toList(),
                                      onChanged: (selectedValues) {
                                        var itemOg = otherOptions[index];
                                        var itemOgId = itemOg.id?.toInt() ?? 0;

                                        // Remove the existing variants for the current item group
                                        setState(() {
                                          variants.removeWhere((variant) =>
                                              variant.optionGroupId ==
                                              itemOgId);
                                        });

                                        // Add the new selected values to the variants
                                        List<Variant> newVariants =
                                            selectedValues.map((value) {
                                          String result = value.split(' - ')[0];
                                          var option = item.options!.firstWhere(
                                              (element) =>
                                                  element.name == result);

                                          return Variant(
                                            optionGroupId: itemOgId,
                                            optionGroupName: itemOg.name ?? "",
                                            optionId: option.id?.toInt() ?? 0,
                                            optionName: value,
                                            offerCheckBoolean: true,
                                          );
                                        }).toList();

                                        setState(() {
                                          variants.addAll(newVariants);
                                        });
                                      },
                                      labelText: item.name ?? 'N/A',
                                      selectedValues: getSelectedValues(
                                          item.id?.toInt() ?? 0),
                                    );
                                  }
                                }

                                if (valueType == "TXTX") {
                                  var itemOg = otherOptions[index];
                                  return TextFormField(
                                    controller:
                                        controllers[itemOg.id?.toInt() ?? 0],
                                    decoration: InputDecoration(
                                      isDense: true,
                                      labelText: itemOg.name ?? 'N/A',
                                      hintText: itemOg.name ?? 'N/A',
                                      labelStyle:
                                          const TextStyle(color: Colors.black),
                                      focusedBorder: border,
                                      enabledBorder: border,
                                      border: border,
                                    ),
                                    onChanged: (value) {
                                      variants.removeWhere((element) =>
                                          element.optionGroupId ==
                                          itemOg.id?.toInt());
                                      variants.add(Variant(
                                        optionGroupId: itemOg.id?.toInt() ?? 0,
                                        optionGroupName: itemOg.name ?? "",
                                        optionId: 0,
                                        optionName: value,
                                        offerCheckBoolean: true,
                                      ));
                                    },
                                  );
                                }
                              }
                              return const SizedBox.shrink();
                            },
                            itemCount: otherOptions.length,
                            separatorBuilder:
                                (BuildContext context, int index) {
                              return const SizedBox(height: 20);
                            },
                          ),
                          const SizedBox(height: 20),
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.black),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Upload / Attach Photo",
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      if (_images.isNotEmpty)
                                        Flexible(
                                          child: SizedBox(
                                            height: 76,
                                            child: ListView.separated(
                                              scrollDirection: Axis.horizontal,
                                              itemCount: _images.length,
                                              padding: const EdgeInsets.all(6),
                                              itemBuilder: (context, index) {
                                                return Stack(
                                                  clipBehavior: Clip.none,
                                                  children: [
                                                    Container(
                                                      decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8)),
                                                      clipBehavior:
                                                          Clip.hardEdge,
                                                      child: kIsWeb
                                                          ? Image.network(
                                                              _images[index],
                                                              width: 64,
                                                              height: 64,
                                                              fit: BoxFit.cover,
                                                            )
                                                          : Image.file(
                                                              File(_images[
                                                                  index]),
                                                              width: 64,
                                                              height: 64,
                                                              fit: BoxFit.cover,
                                                            ),
                                                    ),
                                                    Positioned(
                                                      top: -8,
                                                      right: -8,
                                                      child: InkWell(
                                                        onTap: () {
                                                          setState(() {
                                                            _images.removeAt(
                                                                index);
                                                            _imageBytes
                                                                .removeAt(
                                                                    index);
                                                          });
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                                color:
                                                                    Colors.red),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        16),
                                                            color: Colors
                                                                .transparent,
                                                          ),
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(2),
                                                          child: const Icon(
                                                            Icons.close,
                                                            size: 16,
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              },
                                              separatorBuilder:
                                                  (BuildContext context,
                                                      int index) {
                                                return const SizedBox(
                                                    width: 12);
                                              },
                                            ),
                                          ),
                                        ),
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: Colors.grey.shade200,
                                        ),
                                        child: IconButton(
                                          icon: const Icon(Icons.photo),
                                          onPressed: () {
                                            setState(() {
                                              showImageSourceDialog(context);
                                            });
                                          },
                                        ),
                                      )
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          Container(
                            // height: 56,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.black),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                          child: Text(_audios.isNotEmpty
                                              ? "Voice Note Added"
                                              : "Add Voice Note")),
                                      const Icon(Icons.mic),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Row(children: [
                                    SizedBox(
                                      width: kIsWeb
                                          ? 100
                                          : MediaQuery.of(context).size.width *
                                              0.25,
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                AppColors.primaryColor),
                                        onPressed: () {
                                          getRecorderFn();
                                        },
                                        child: Text(_mRecorder!.isRecording
                                            ? 'Stop'
                                            : 'Record'),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 20,
                                    ),
                                    Text(_mRecorder!.isRecording
                                        ? 'Recording in progress'
                                        : 'Recorder is stopped'),
                                  ]),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          if (_audios.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.all(8),
                              height: 80,
                              width: double.infinity,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.black),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: kIsWeb
                                        ? 100
                                        : MediaQuery.of(context).size.width *
                                            0.25,
                                    child: ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              AppColors.primaryColor),
                                      onPressed: () {
                                        getPlaybackFn();
                                      },
                                      child: Text(_mPlayer!.isPlaying
                                          ? 'Stop'
                                          : 'Play'),
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 20,
                                  ),
                                  Text(_mPlayer!.isPlaying
                                      ? 'Playback in progress'
                                      : 'Player is stopped'),
                                  const Spacer(),
                                  IconButton(
                                    onPressed: () {
                                      setState(() {
                                        _audios.clear();
                                      });
                                    },
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (_audios.isNotEmpty) const SizedBox(height: 20),
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.black),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Upload Additional Specifications / Docs",
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      if (_files.isNotEmpty)
                                        Flexible(
                                          child: SizedBox(
                                            height: 76,
                                            child: ListView.separated(
                                              scrollDirection: Axis.horizontal,
                                              itemCount: _files.length,
                                              padding: const EdgeInsets.all(6),
                                              itemBuilder: (context, index) {
                                                return Stack(
                                                  clipBehavior: Clip.none,
                                                  children: [
                                                    Container(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              8),
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                        color: Colors
                                                            .grey.shade200,
                                                      ),
                                                      child: IconButton(
                                                        icon: Icon(
                                                            Icons
                                                                .file_copy_outlined,
                                                            color: AppColors
                                                                .primaryColor),
                                                        onPressed: () {},
                                                      ),
                                                    ),
                                                    Positioned(
                                                      top: -8,
                                                      right: -8,
                                                      child: GestureDetector(
                                                        onTap: () {
                                                          setState(() {
                                                            _files.removeAt(
                                                                index);
                                                            _fileBytes.removeAt(
                                                                index);
                                                          });
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                                color:
                                                                    Colors.red),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        16),
                                                          ),
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(2),
                                                          child: const Icon(
                                                            Icons.close,
                                                            size: 16,
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              },
                                              separatorBuilder:
                                                  (BuildContext context,
                                                      int index) {
                                                return const SizedBox(
                                                    width: 12);
                                              },
                                            ),
                                          ),
                                        ),
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: Colors.grey.shade200,
                                        ),
                                        child: IconButton(
                                          icon: const Icon(
                                              Icons.file_upload_outlined),
                                          onPressed: () {
                                            setState(() {
                                              pickFiles();
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          TextField(
                            controller: _instructionsController,
                            textAlign: TextAlign.start,
                            maxLength: 500,
                            decoration: InputDecoration(
                              labelText: "Add Instructions",
                              alignLabelWithHint: true,
                              focusedBorder: border,
                              enabledBorder: border,
                              border: border,
                              labelStyle: const TextStyle(color: Colors.black),
                              isDense: true,
                            ),
                            maxLines: 4,
                          ),
                          if (!isAddedToCart) const SizedBox(height: 20),
                          if (!isAddedToCart)
                            BaiButton(
                              onTap: () {
                                if (_formKey.currentState!.validate()) {
                                  var poStockItem = PoStockItem(
                                    mvtItemId: item.id?.toInt() ?? 0,
                                    mvtItemName: widget.isGeneral
                                        ? _productNameController.text
                                        : "${item.name}",
                                    quantity: int.tryParse(
                                            _quantityController.text) ??
                                        1,
                                    price: null,
                                    unitId:
                                        int.tryParse(_selectedUnitId ?? "") ??
                                            1,
                                    instructions: _instructionsController.text,
                                    status: "PENDING",
                                    variants: variants,
                                    cappCategoriesId: widget.isGeneral
                                        ? -3
                                        : (state.response?.data?.serviceId
                                                ?.toInt() ??
                                            0),
                                    prchOrdrSplitId: widget.splitGroupId,
                                  );

                                  var stockItem = StockItem(
                                    poStockItem: poStockItem,
                                    audios: _audios,
                                    images: _images,
                                    imageBytes: _imageBytes,
                                    files: _files,
                                    fileBytes: _fileBytes,
                                    selectedUnit: _selectedUnit,
                                  );
                                  BaiCart.cartItems.add(stockItem);
                                  setState(() {
                                    isAddedToCart = true;
                                  });
                                  _scrollToBottom();
                                }
                              },
                              text: "ADD PRODUCT",
                              backgoundColor: Colors.green.shade900,
                            ),
                          if (isAddedToCart) const SizedBox(height: 20),
                          if (isAddedToCart && BaiCart.cartItems.isNotEmpty)
                            (1 == 2)
                                // Split items buttons
                                ? Column(
                                    children: [
                                      BaiButton(
                                        onTap: () {
                                          Get.until((route) =>
                                              route.settings.name ==
                                              AppRoutes.offlineScreen);
                                        },
                                        backgoundColor: AppColors.primaryColor,
                                        text: "NEXT SPLIT",
                                      ),
                                      const SizedBox(height: 20),
                                      BaiButton(
                                        onTap: () {
                                          Get.to(CheckOut(
                                            isMr: widget.isMr,
                                            deliveryDate: widget.deliveryDate,
                                          ));
                                        },
                                        backgoundColor: Colors.green.shade900,
                                        text: "COMPLETE SPLIT",
                                      ),
                                    ],
                                  )
                                // Regular buttons
                                : Column(
                                    children: [
                                      BaiButton(
                                        onTap: () {
                                          Navigator.pop(context);
                                        },
                                        backgoundColor: AppColors.primaryColor,
                                        text: "ADD MORE PRODUCTS",
                                      ),
                                      const SizedBox(height: 20),
                                      BaiButton(
                                        onTap: () {
                                          if (BaiCart.cartItems.isEmpty) {
                                            alert(
                                                "Your material request list is empty.");
                                            return;
                                          }
                                          BaiCart.orderGroupId =
                                              widget.orderGroupId;
                                          Navigator.pop(context);
                                          Get.to(CheckOut(
                                            isMr: widget.isMr,
                                            deliveryDate: widget.deliveryDate,
                                          ));
                                        },
                                        backgoundColor: Colors.green.shade900,
                                        // text: widget.splitGroupName != null
                                        //     ? "SHOW ${widget.isMr ? "MATERIAL" : "SERVICE"} REQUEST"
                                        //     : "CREATE ${widget.isMr ? "MATERIAL" : "SERVICE"} REQUEST",
                                        text: "NEXT > DELIVERY DETAILS",
                                      ),
                                    ],
                                  ),
                        ],
                      ),
                    ),
                  ));
            } else {
              return const SizedBox.shrink();
            }
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _mPlayer!.closePlayer();
    _mPlayer = null;

    _mRecorder!.closeRecorder();
    _mRecorder = null;
    super.dispose();
  }

  void record() {
    _mRecorder!
        .startRecorder(
      toFile: _mPath,
      codec: _codec,
      audioSource: theSource,
    )
        .then((value) {
      setState(() {});
    });
  }

  void stopRecorder() async {
    await _mRecorder!.stopRecorder().then((value) {
      setState(() {
        //var url = value;
        if (hasAttachmentsMaxed()) {
          _audios.clear();
          _mplaybackReady = true;
          alert(
              "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
        } else {
          _audios.clear();
          if (value != null) {
            print("value: $value");
            _audios.add(value);
          }
          _mplaybackReady = true;
        }
      });
    });
  }

  void play() {
    assert(_mPlayerIsInited &&
        _mplaybackReady &&
        _mRecorder!.isStopped &&
        _mPlayer!.isStopped);
    _mPlayer!
        .startPlayer(
            fromURI: _mPath,
            whenFinished: () {
              setState(() {});
            })
        .then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer!.stopPlayer().then((value) {
      setState(() {});
    });
  }

  getRecorderFn() {
    if (!_mRecorderIsInited || !_mPlayer!.isStopped) {
      return null;
    }

    return _mRecorder!.isStopped ? record() : stopRecorder();
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mplaybackReady || !_mRecorder!.isStopped) {
      return null;
    }

    return _mPlayer!.isStopped ? play() : stopPlayer();
  }
}

String formatDateToDDMMYYYY(DateTime dateTime) {
  final DateFormat formatter = DateFormat('dd-MM-yyyy');
  return formatter.format(dateTime);
}
