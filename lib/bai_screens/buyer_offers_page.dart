import 'dart:ui';

import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_models/approve_po_req.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/buyer_card.dart';
import 'package:connectone/bai_models/invite_status_res.dart';
import 'package:connectone/bai_models/offers_filter_res.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_models/summary_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/core/bai_widgets/negotiate_dialog.dart';
// import 'package:connectone/bai_screens/quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/negotiation_card.dart';
import 'package:connectone/core/bai_widgets/quote_summary_widgets.dart';
import 'package:connectone/core/bai_widgets/quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/status_dialog.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/others.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_blocs/offline_stocks/offline_stocks_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/assign_dialog.dart';
import '../core/bai_widgets/filter_popup.dart';
import '../core/bai_widgets/help_info.dart';

class BuyerOffersPage extends StatefulWidget {
  const BuyerOffersPage({
    Key? key,
    required this.item,
    required this.showSummary,
    this.changeStatusDialog,
    this.selectedIndex,
    this.enablePoSummary = false,
    this.buttonText,
    this.deliveryDate,
    required this.categoryId,
    required this.splitName,
    required this.steelMr,
    required this.splitStatus,
    // required this.prchOrdrId,
  }) : super(key: key);

  final Content item;
  final bool showSummary;
  final int? selectedIndex;
  final ChangeStatusDialog? changeStatusDialog;
  final bool? enablePoSummary;
  final String? buttonText;
  final DateTime? deliveryDate;
  final String? categoryId;
  final String? splitName;
  final bool steelMr;
  final String? splitStatus;
  // final String? prchOrdrId;
  @override
  State<BuyerOffersPage> createState() => _BuyerOffersPageState();
}

class _BuyerOffersPageState extends State<BuyerOffersPage> {
  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  var api = NetworkController();

  var req = ViewOfferReq(
    variant1OptionGroupName: [],
    variant1OptionNames: [],
    variant2OptionGroupName: [],
    variant2OptionNames: [],
    variant3OptionGroupName: [],
    variant3OptionNames: [],
  );

  OffersFilterRes filter = OffersFilterRes(
    variant1OptionGroupName: [],
    variant1OptionNames: [],
    variant2OptionGroupName: [],
    variant2OptionNames: [],
    variant3OptionGroupName: [],
    variant3OptionNames: [],
  );

  bool showQuantity() {
    return !(widget.item.mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement");
  }

  @override
  void initState() {
    // print('--------widget.prchOrdrId ${widget.prchOrdrId}');

    OrderStorage.clearOrderPairs();
    context.read<OffersCubit>().loadBuyerOffers(
          widget.item.prchOrdrId.toString(),
          req,
          widget.item.orderGroupId?.toInt() ?? 0,
          widget.categoryId ?? widget.item.cappCategoriesId?.toString() ?? "",
          enablePoSummary: widget.enablePoSummary ?? false,
          date: widget.deliveryDate ?? DateTime.now(),
          steelMr: widget.steelMr,
        );
    _selectedIndex = widget.showSummary ? 0 : (widget.selectedIndex ?? 0);
    createTutorial();
    super.initState();
  }

  var quantity = 0;
  String? nextStatus;
  int _selectedIndex = 0;

  List<Offer> quotes = [];
  List<Offer> negotiations = [];
  List<SummaryResponse> summaryList = [];

  bool hideProductName() {
    if (_selectedIndex == 0) {
      return true;
    }
    return false;
  }

  @override

  /// Builds the widget tree for [BuyerOffersPage].
  ///
  /// Displays a [Scaffold] with an [AppBar] and a [Body] that contains a
  /// [RefreshIndicator] and a [Column].
  ///
  /// The [Column] contains a [Row] with a [Text] and an [IconButton] to filter
  /// the offers, and a [TabBar] with three tabs: "Summary", "Quotes", and
  /// "Negotiations".
  ///
  /// The content of the tabs is as follows:
  ///
  /// * "Summary": displays a [ListView] with a [SummaryTable] for each summary
  ///   in [state.summary].
  /// * "Quotes": displays a [ListView] with a [BuyerCard] for each quote in
  ///   [state.offers.offers].
  /// * "Negotiations": displays a [ListView] with a [NegotiationCard] for each
  ///   negotiation in [state.offers.offers].
  ///
  /// The [BuyerCard] and [NegotiationCard] widgets display the details of the
  /// offer and allow the user to accept or reject the offer.
  ///
  /// The [SummaryTable] widget displays the summary of the offers and allows
  /// the user to select a vendor.
  ///
  Widget build(BuildContext context) {
    // print('--------widget.prchOrdrId ${widget.prchOrdrId}');
    var roleCode = getRoleLevel();
    //site edited
    var isSiteInCharge = roleCode == 15;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // alert(getVendorId());
            Navigator.pop(context);
          },
        ),
        title: isSiteInCharge ? Text("Order Summary") : Text("Quote Summary"),
        backgroundColor: AppColors.primaryColor,
        actions: [
          // ReportButton(
          //   prchOrdrId: widget.item.prchOrdrId.toString(),
          // ),
          // IconButton(
          //   onPressed: () {
          //     showTutorial();
          //   },
          //   icon: const Icon(
          //     Icons.help_center,
          //   ),
          // ),
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          ),
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<OffersCubit, OffersState>(
          listener: (context, state) {
            if (state is RefreshBuyerPage) {
              context.read<OffersCubit>().loadBuyerOffers(
                    widget.item.prchOrdrId.toString(),
                    req,
                    widget.item.orderGroupId?.toInt() ?? 0,
                    widget.categoryId ??
                        widget.item.cappCategoriesId?.toString() ??
                        "",
                    enablePoSummary: widget.enablePoSummary ?? false,
                    date: widget.deliveryDate ?? DateTime.now(),
                    steelMr: widget.steelMr,
                  );
            }
            if (state is BuyerOffersAccepted) {
              OrderStorage.clearOrderPairs();
              properAlert(state.message);
              context.read<OffersCubit>().loadBuyerOffers(
                    widget.item.prchOrdrId.toString(),
                    req,
                    widget.item.orderGroupId?.toInt() ?? 0,
                    widget.categoryId ??
                        widget.item.cappCategoriesId?.toString() ??
                        "",
                    enablePoSummary: widget.enablePoSummary ?? false,
                    date: widget.deliveryDate ?? DateTime.now(),
                    steelMr: widget.steelMr,
                  );
            }
            if (state is BuyerOffersNegotiated) {
              properAlert(state.message);
              context.read<OffersCubit>().loadBuyerOffers(
                    widget.item.prchOrdrId.toString(),
                    req,
                    widget.item.orderGroupId?.toInt() ?? 0,
                    widget.categoryId ??
                        widget.item.cappCategoriesId?.toString() ??
                        "",
                    enablePoSummary: widget.enablePoSummary ?? false,
                    date: widget.deliveryDate ?? DateTime.now(),
                    steelMr: widget.steelMr,
                  );
            }
            if (state is DiscardSuccess) {
              alert(state.message);
              context.read<OffersCubit>().loadBuyerOffers(
                    widget.item.prchOrdrId.toString(),
                    req,
                    widget.item.orderGroupId?.toInt() ?? 0,
                    widget.categoryId ??
                        widget.item.cappCategoriesId?.toString() ??
                        "",
                    enablePoSummary: widget.enablePoSummary ?? false,
                    date: widget.deliveryDate ?? DateTime.now(),
                    steelMr: widget.steelMr,
                  );
            }
            if (state is AcceptNegotiateFailed) {
              properAlert(state.message);
              context.read<OffersCubit>().loadBuyerOffers(
                    widget.item.prchOrdrId.toString(),
                    req,
                    widget.item.orderGroupId?.toInt() ?? 0,
                    widget.categoryId ??
                        widget.item.cappCategoriesId?.toString() ??
                        "",
                    enablePoSummary: widget.enablePoSummary ?? false,
                    date: widget.deliveryDate ?? DateTime.now(),
                    steelMr: widget.steelMr,
                  );
            }
            if (state is NegotiationAccepted) {
              properAlert(state.message);
              context.read<OffersCubit>().loadBuyerOffers(
                    widget.item.prchOrdrId.toString(),
                    req,
                    widget.item.orderGroupId?.toInt() ?? 0,
                    widget.categoryId ??
                        widget.item.cappCategoriesId?.toString() ??
                        "",
                    enablePoSummary: widget.enablePoSummary ?? false,
                    date: widget.deliveryDate ?? DateTime.now(),
                    steelMr: widget.steelMr,
                  );
            }
            if (state is SellerOffersLoaded) {
              setState(() {
                summaryList = state.summary;
                nextStatus =
                    state.summary[0].summary?.prchOrdrSplitStatus ?? 'N/A';
                filter = state.filter;
                quantity = widget.item.quantity?.toInt() ?? 0;
                quotes = state.offers.offers ?? [];
                negotiations = state.offers.offers
                        ?.where((e) => e.negotiationHistory?.isNotEmpty == true)
                        .toList() ??
                    [];
              });
            }
          },
          builder: (context, state) {
            (state is SellerOffersLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            if (state is SellerOffersLoaded) {
              print(
                  '--------state.offers.mrStatus ${widget.splitStatus}----${state.summary[0].summary?.submitOfferYN}');
              var isSelectVendorEnabled =state.summary[0].summary?.showSelectVendorButton == 'Y';
              var isInviteVendorEnabled =state.summary[0].summary?.showInviteVendorButton == 'Y';
                  // ['QUOT', 'NEGO'].contains(widget.splitStatus);
              var statuses = state.nextStatus?.data ?? [];
              return Stack(
                children: [
                  RefreshIndicator(
                    onRefresh: () {
                      context.read<OffersCubit>().loadBuyerOffers(
                            widget.item.prchOrdrId.toString(),
                            req,
                            widget.item.orderGroupId?.toInt() ?? 0,
                            widget.categoryId ??
                                widget.item.cappCategoriesId?.toString() ??
                                "",
                            enablePoSummary: widget.enablePoSummary ?? false,
                            date: widget.deliveryDate ?? DateTime.now(),
                            steelMr: widget.steelMr,
                          );
                      return Future.value();
                    },
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 16,
                            left: 16,
                            right: 16,
                            bottom: 4,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: hideProductName()
                                        ? const SizedBox()
                                        : Text(
                                            '${widget.item.mvtItemName}',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            maxLines: 3,
                                          ),
                                  ),
                                  hideProductName()
                                      ? const SizedBox()
                                      : IconButton(
                                          constraints: const BoxConstraints(),
                                          padding: EdgeInsets.zero,
                                          icon: const Icon(
                                              Icons.filter_alt_outlined),
                                          onPressed: () {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                return FilterPopup(
                                                  data: filter,
                                                  viewOfferReq: req,
                                                  onApply:
                                                      (ViewOfferReq filterReq) {
                                                    req = filterReq;
                                                    context
                                                        .read<OffersCubit>()
                                                        .loadBuyerOffers(
                                                          widget.item.prchOrdrId
                                                              .toString(),
                                                          filterReq,
                                                          widget.item
                                                                  .orderGroupId
                                                                  ?.toInt() ??
                                                              0,
                                                          widget.categoryId ??
                                                              widget.item
                                                                  .cappCategoriesId
                                                                  ?.toString() ??
                                                              "",
                                                          enablePoSummary: widget
                                                                  .enablePoSummary ??
                                                              false,
                                                          date: widget
                                                                  .deliveryDate ??
                                                              DateTime.now(),
                                                          steelMr:
                                                              widget.steelMr,
                                                        );
                                                  },
                                                );
                                              },
                                            );
                                          },
                                        ),
                                ],
                              ),
                              if (showQuantity())
                                hideProductName()
                                    ? const SizedBox()
                                    : const SizedBox(height: 4),
                              if (showQuantity())
                                hideProductName()
                                    ? const SizedBox()
                                    : Text(
                                        'Quantity: ${quantity.toStringAsFixed(0)} ${widget.item.optionName ?? ""}',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                            ],
                          ),
                        ),
                        // Existing content inside the Column
                        // state.offers.offers?.isNotEmpty == true
                        //     ?
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Column(
                              children: [
                                // Container(
                                //   height: 64,
                                //   color: AppColors.white,
                                //   child: Container(
                                //     margin:
                                //         const EdgeInsets.fromLTRB(6, 16, 6, 8),
                                //     child: Align(
                                //       alignment: Alignment.centerLeft,
                                //       child: SingleChildScrollView(
                                //         scrollDirection: Axis.horizontal,
                                //         child: Row(
                                //           children: [
                                //             TabItem(
                                //               key: key1,
                                //               text: "Summary",
                                //               index: 0,
                                //               selectedColor:
                                //                   AppColors.primaryColorOld,
                                //               unselectedColor: AppColors.white,
                                //               selectedTextColor:
                                //                   AppColors.white,
                                //               unselectedTextColor: Colors.black,
                                //               selectedIndex: _selectedIndex,
                                //               onTap: (index) {
                                //                 setState(() {
                                //                   _selectedIndex = index;
                                //                 });
                                //               },
                                //               fontSize: 14,
                                //               width: kIsWeb
                                //                   ? 150
                                //                   : ((MediaQuery.of(context)
                                //                               .size
                                //                               .width -
                                //                           20) /
                                //                       3),
                                //             ),
                                //             if (!widget.showSummary)
                                //               TabItem(
                                //                 key: key2,
                                //                 text: "Quotes",
                                //                 index: 1,
                                //                 selectedColor:
                                //                     AppColors.primaryColorOld,
                                //                 unselectedColor:
                                //                     AppColors.white,
                                //                 selectedTextColor:
                                //                     AppColors.white,
                                //                 unselectedTextColor:
                                //                     Colors.black,
                                //                 selectedIndex: _selectedIndex,
                                //                 onTap: (index) {
                                //                   setState(() {
                                //                     _selectedIndex = index;
                                //                   });
                                //                 },
                                //                 fontSize: 14,
                                //                 width: kIsWeb
                                //                     ? 150
                                //                     : ((MediaQuery.of(context)
                                //                                 .size
                                //                                 .width -
                                //                             20) /
                                //                         3),
                                //               ),
                                //             if (!widget.showSummary)
                                //               TabItem(
                                //                 key: key3,
                                //                 text: "Negotiation",
                                //                 index: 2,
                                //                 selectedColor:
                                //                     AppColors.primaryColorOld,
                                //                 unselectedColor:
                                //                     AppColors.white,
                                //                 selectedTextColor:
                                //                     AppColors.white,
                                //                 unselectedTextColor:
                                //                     Colors.black,
                                //                 selectedIndex: _selectedIndex,
                                //                 onTap: (index) {
                                //                   setState(() {
                                //                     _selectedIndex = index;
                                //                   });
                                //                 },
                                //                 fontSize: 14,
                                //                 width: kIsWeb
                                //                     ? 150
                                //                     : ((MediaQuery.of(context)
                                //                                 .size
                                //                                 .width -
                                //                             20) /
                                //                         3),
                                //               ),
                                //           ],
                                //         ),
                                //       ),
                                //     ),
                                //   ),
                                // ),
                                Expanded(
                                  child:
                                      // All Offers Tab (ListView)
                                      _selectedIndex == 0
                                          ? summaryList.isEmpty
                                              ? SizedBox(
                                                  height: MediaQuery.of(context)
                                                          .size
                                                          .height /
                                                      1.5,
                                                  child: Center(
                                                    child: Text(
                                                      "No summary found",
                                                      style: largeStyle,
                                                    ),
                                                  ),
                                                )
                                              : Scrollbar(
                                                  child: ListView.separated(
                                                    padding:
                                                        const EdgeInsets.only(
                                                      left: 2,
                                                      right: 2,
                                                      bottom: 40,
                                                    ),
                                                    itemCount:
                                                        summaryList.length,
                                                    shrinkWrap: true,
                                                    itemBuilder:
                                                        (context, index) {
                                                      return Card(
                                                        elevation: 0,
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        6),
                                                            color:
                                                                //  isApproved ?
                                                                //  Colors.green.shade100 ,
                                                                Colors.white,
                                                            boxShadow: [
                                                              BoxShadow(
                                                                color: AppColors
                                                                    .primaryColor
                                                                    .withOpacity(
                                                                        0.2),
                                                                offset:
                                                                    const Offset(
                                                                        0, 2),
                                                                blurRadius: 6,
                                                                spreadRadius: 2,
                                                              ),
                                                            ],
                                                          ),
                                                          margin:
                                                              const EdgeInsets
                                                                  .all(1),
                                                          child: Column(
                                                            children: [
                                                              SummaryTable(
                                                                res:
                                                                    summaryList,
                                                                index: index,
                                                                prchOrdrOffrIds:
                                                                    (val) {},
                                                                content:
                                                                    widget.item,
                                                                statusCd: state
                                                                        .offers
                                                                        .mrStatus ??
                                                                    "",
                                                                date: widget
                                                                        .deliveryDate ??
                                                                    DateTime
                                                                        .now(),
                                                                splitName: widget
                                                                    .splitName,
                                                                categoryId: widget
                                                                    .categoryId,
                                                                steelMr: widget
                                                                    .steelMr,
                                                              ),
                                                              if (isSelectVendorEnabled)
                                                                const SizedBox(
                                                                    height: 16),
                                                              if (isSelectVendorEnabled)
                                                                BaiButton(
                                                                  height: 40,
                                                                  onTap: () {
                                                                    var sellerId = summaryList[index]
                                                                            .vendorGroupedProducts?[0]
                                                                            .matchedProducts
                                                                            ?.first
                                                                            .details
                                                                            ?.first
                                                                            .sellerId ??
                                                                        0;
                                                                    var offrIds = OrderStorage
                                                                            .getOrderPairs()
                                                                        .where((e) =>
                                                                            e.sellerId ==
                                                                            sellerId
                                                                                .toString())
                                                                        .map((e) =>
                                                                            int.parse(e.prchOrdrOffrId))
                                                                        .toList();
                                                                    var req = ApprovePoReq(
                                                                        prchOrdrOfferIds:
                                                                            offrIds);
                                                                    context
                                                                        .read<
                                                                            OffersCubit>()
                                                                        .acceptOffer(
                                                                            req);
                                                                  },
                                                                  text:
                                                                      "SELECT VENDOR",
                                                                  backgoundColor: AppColors
                                                                      .primaryColorOld
                                                                      .withOpacity(
                                                                          0.8),
                                                                ),
                                                              // if (quotes
                                                              //     .isNotEmpty)
                                                              //   const SizedBox(
                                                              //       height: 10),
                                                              // if (quotes
                                                              //     .isNotEmpty)
                                                              //   BaiButton(
                                                              //     height: 40,
                                                              //     backgoundColor:
                                                              //         Colors
                                                              //             .teal,
                                                              //     onTap: () {
                                                              //       context
                                                              //           .read<
                                                              //               OffersCubit>()
                                                              //           .sendEmail(
                                                              //             widget
                                                              //                 .item
                                                              //                 .orderGroupId
                                                              //                 .toString(),
                                                              //             widget.deliveryDate ??
                                                              //                 DateTime.now(),
                                                              //           );
                                                              //     },
                                                              //     text:
                                                              //         "EMAIL QUOTE",
                                                              //     //  backgoundColor: AppColors.primaryColorOld,
                                                              //   ),

                                                              // if (state
                                                              //         .showInviteButton ==
                                                              //     true)
                                                              if (isInviteVendorEnabled)
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          top:
                                                                              8.0),
                                                                  child:
                                                                      BaiButton(
                                                                    onTap: () {
                                                                      // if ((widget.content.cappCategoriesId ?? 0) < 0) {
                                                                      //   properAlert(
                                                                      //       "Can not assign vendors to General category MR. \n\nPlease assign a category to the MR.");
                                                                      //   return;
                                                                      // }
                                                                      Navigator.pop(
                                                                          context);
                                                                      showDialog(
                                                                          context:
                                                                              context,
                                                                          builder:
                                                                              (context) {
                                                                            return AssignDialog(
                                                                              content: widget.item,
                                                                              reload: () {},
                                                                              changeStatus: () {},
                                                                              categoryId: widget.categoryId,
                                                                              date: widget.deliveryDate ?? DateTime.now(),
                                                                            );
                                                                          });
                                                                    },
                                                                    text:
                                                                        "INVITE VENDORS",
                                                                    height: 40,
                                                                    backgoundColor:
                                                                        Colors
                                                                            .green
                                                                            .shade800,
                                                                  ),
                                                                ),
                                                              //  if (state
                                                              //     .showInviteButton ==
                                                              // false &&
                                                              // summaryList[index]
                                                              //         .summary
                                                              //         ?.prchOrdrSplitStatus?.length!=0
                                                              //     )

                                                              //                                                           ( state.summary[0].summary?.submitOfferYN == 'N') ?
                                                              //                                                             Padding(
                                                              //                                     padding: const  EdgeInsets
                                                              //                                                                       .only(
                                                              //                                                                       top:
                                                              //                                                                           8.0),
                                                              //                                     child: Container(width: double.infinity,
                                                              //                                       height: 40,
                                                              //                                       decoration: BoxDecoration(
                                                              //                                         color: AppColors.green,
                                                              //                                         borderRadius:
                                                              //                                             BorderRadius.circular(4),
                                                              //                                             border: Border.all(color: AppColors.green),
                                                              //                                       ),
                                                              //                                       child: TextButton(
                                                              //                                         style: const ButtonStyle(
                                                              //                                             // Change text color
                                                              //                                             ),
                                                              //                                         onPressed: () async {
                                                              //                                           print(nextStatus);

                                                              //                                           // try {
                                                              //                                             // Get all offer IDs from OrderStorage
                                                              //                                             var allOfferIds = OrderStorage.getOrderPairs()
                                                              //                                                 .map((e) => int.parse(e.prchOrdrOffrId))
                                                              //                                                 .toList();

                                                              //                                             if (allOfferIds.isEmpty) {
                                                              //                                               alert("No offers selected. Please select offers before submitting.");
                                                              //                                               return;
                                                              //                                             }

                                                              //                                             // Call the notify-action API
                                                              //                                             await api.notifyAction(
                                                              //                                               prchOrdrSplitId: widget.item.prchOrdrSplitId?.toInt() ?? 0,
                                                              //                                               prchOrdrId: widget.item.prchOrdrId?.toInt() ?? 0,
                                                              //                                               statusCd: 'QUOT',
                                                              //                                               offerId: allOfferIds,
                                                              //                                             );
                                                              //                                             OrderStorage.clearOrderPairs();
                                                              // context.read<OffersCubit>().loadBuyerOffers(
                                                              //      widget.item.prchOrdrId.toString(),
                                                              //       req,
                                                              //       widget.item.orderGroupId?.toInt() ?? 0,
                                                              //       widget.categoryId ?? widget.item.cappCategoriesId?.toString() ?? "",
                                                              //       enablePoSummary: widget.enablePoSummary ?? false,
                                                              //       date: widget.deliveryDate ?? DateTime.now(),
                                                              //       steelMr: widget.steelMr,
                                                              //     );
                                                              //                                             // context.read<OfflineMainBloc>().add(RefreshOfflineStocks());

                                                              //                                             alert("Submit action completed successfully!");

                                                              //                                             // Refresh the page after successful submission
                                                              //                                             // context.read<OffersCubit>().loadBuyerOffers(
                                                              //                                             //   widget.item.prchOrdrId.toString(),
                                                              //                                             //   req,
                                                              //                                             //   widget.item.orderGroupId?.toInt() ?? 0,
                                                              //                                             //   widget.categoryId ?? widget.item.cappCategoriesId?.toString() ?? "",
                                                              //                                             //   enablePoSummary: widget.enablePoSummary ?? false,
                                                              //                                             //   date: widget.deliveryDate ?? DateTime.now(),
                                                              //                                             //   steelMr: widget.steelMr,
                                                              //                                             // );
                                                              //                                           // } catch (e) {
                                                              //                                           //   alert("Error submitting: ${e.toString()}");
                                                              //                                           // }
                                                              //                                         },

                                                              //                                         child: Text('Submit'

                                                              //                                           ,
                                                              //                                           style: const TextStyle(
                                                              //                                             color: Colors.white,
                                                              //                                             fontSize: 16,
                                                              //                                             fontWeight: FontWeight.bold,
                                                              //                                           ),
                                                              //                                         ),
                                                              //                                       ),
                                                              //                                     ),
                                                              //                                   )
                                                              //                                   :SizedBox(),
                                                              Padding(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .only(
                                                                        top:
                                                                            8.0),
                                                                child:
                                                                    Container(
                                                                  width: double
                                                                      .infinity,
                                                                  height: 40,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: AppColors
                                                                        .white,
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(4),
                                                                    border: Border.all(
                                                                        color: Colors
                                                                            .grey),
                                                                  ),
                                                                  child:
                                                                      TextButton(
                                                                    style: const ButtonStyle(
                                                                        // Change text color
                                                                        ),
                                                                    onPressed:
                                                                        () {
                                                                      print(
                                                                          nextStatus);
                                                                      // context
                                                                      //     .read<OfflineMainBloc>()
                                                                      //     .add(ItemSelected(widget
                                                                      //             .currentItem
                                                                      //             .prchOrdrId
                                                                      //             ?.toInt() ??
                                                                      //         0));
                                                                      // context
                                                                      //     .read<OfflineSubBloc>()
                                                                      //     .add(const ToggleCard());
                                                                      // setState(() {
                                                                      //   openData();

                                                                      //   isRead = true;
                                                                      // });
                                                                    },
                                                                    child: Text(
                                                                      nextStatus=='PO Generated'?'Status : ${state.summary[0].summary?.prchOrdrSplitStatus} - ${widget.categoryId ?? widget.item.cappCategoriesId?.toString()}'
:
                                                                      'Status : $nextStatus',
                                                                      style:
                                                                          const TextStyle(
                                                                        color: Colors
                                                                            .black,
                                                                        fontSize:
                                                                            16,
                                                                        fontWeight:
                                                                            FontWeight.bold,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              //                         if (summaryList[
                                                              //                                     index]
                                                              //                                 .summary
                                                              //                                 ?.vendorName !=
                                                              //                             null)

                                                              //                                                       BaiButton(onTap: ()async{
                                                              //                                                         var date = await showDialog(
                                                              //     context: context,
                                                              //     builder: (context) {
                                                              //       return MRSummaryDialog(
                                                              //         orderGroupId: widget.item.orderGroupId.toString(),
                                                              //         prchOrdrSplitId: widget.item.prchOrdrSplitId.toString(),
                                                              //         categoryId: widget.item.cappCategoriesId.toString(),
                                                              //       );
                                                              //     });
                                                              // if (date[0] == null) {
                                                              //   return;
                                                              // }

                                                              // // Check if the widget is still mounted before showing the dialog
                                                              // if (context.mounted) {
                                                              //   showDialog(
                                                              //       context: context,
                                                              //       builder: (context) {
                                                              //         return QuoteSummaryDialog(
                                                              //           prchOrdrId: widget.item.orderGroupId?.toInt() ?? 0,
                                                              //           content: widget.item,
                                                              //           date: date[0],
                                                              //           categoryId: date[1]?.toString(),
                                                              //           steelMr: date[3] ?? false,
                                                              //         );
                                                              //       });
                                                              // }},text: "INVITE VENDOR",),

                                                              //                                                     showDialog(
                                                              // context: context,
                                                              // builder: (context) {
                                                              //   return InviteStatusDialog(content: widget.item);
                                                              // });
                                                              // return;
                                                              //                                                   }, text: "INVITE VENDOR",),
                                                              for (var status
                                                                  in statuses)
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          top:
                                                                              10),
                                                                  child:
                                                                      BaiButton(
                                                                    height: 40,
                                                                    backgoundColor:
                                                                        getMediumColor(
                                                                            statuses.indexOf(status)),
                                                                    onTap: () {
                                                                      var sellerId = summaryList[index]
                                                                              .vendorGroupedProducts?[0]
                                                                              .matchedProducts
                                                                              ?.first
                                                                              .details
                                                                              ?.first
                                                                              .sellerId ??
                                                                          0;
                                                                      var summary =
                                                                          summaryList[index]
                                                                              .summary;
                                                                      var offrIds = OrderStorage
                                                                              .getOrderPairs()
                                                                          .where((e) =>
                                                                              e.sellerId ==
                                                                              sellerId
                                                                                  .toString())
                                                                          .map((e) => int.parse(e
                                                                              .prchOrdrId
                                                                              .toString()))
                                                                          .toList();
                                                                      var statusChangeDialog =
                                                                          ChangeStatusDialog(
                                                                        prchOrdrId:
                                                                            offrIds,
                                                                        content:
                                                                            widget.item,
                                                                        vendorId:
                                                                            widget.item.sellerVendorId ??
                                                                                0,
                                                                        enableAudio:
                                                                            status.enableAudio ??
                                                                                false,
                                                                        enablePictures:
                                                                            status.enablePictures ??
                                                                                false,
                                                                        enableComments:
                                                                            status.enableComments ??
                                                                                false,
                                                                        enableLocation:
                                                                            status.enableLocation ??
                                                                                false,
                                                                        reload:
                                                                            () {},
                                                                        statusCd:
                                                                            status.statusCode ??
                                                                                '',
                                                                        statusName:
                                                                            status.buttonText ??
                                                                                '',
                                                                        assignedVendorId:
                                                                            int.tryParse(sellerId.toString()),
                                                                        adminEmail:
                                                                            summary?.adminEmail,
                                                                        vendorEmail:
                                                                            summary?.vendorEmail,
                                                                      );
                                                                      showDialog(
                                                                          context:
                                                                              context,
                                                                          builder:
                                                                              (context) {
                                                                            return statusChangeDialog;
                                                                          });
                                                                    },
                                                                    text: status
                                                                            .buttonText
                                                                            ?.toUpperCase() ??
                                                                        "N/A",
                                                                  ),
                                                                ),
                                                              // if (widget.changeStatusDialog != null)
                                                              //   const SizedBox(height: 16),
                                                              // if (widget.changeStatusDialog != null)
                                                              //   BaiButton(
                                                              //     height: 40,
                                                              //     backgoundColor: AppColors.green,
                                                              //     onTap: () {
                                                              //       var sellerId = summaryList[index]
                                                              //               .vendorGroupedProducts?[0]
                                                              //               .matchedProducts
                                                              //               ?.first
                                                              //               .details
                                                              //               ?.first
                                                              //               .sellerId ??
                                                              //           0;
                                                              //       var offrIds = OrderStorage.getOrderPairs()
                                                              //           .where((e) => e.sellerId == sellerId.toString())
                                                              //           .map((e) => int.parse(e.prchOrdrId.toString()))
                                                              //           .toList();

                                                              //       widget.changeStatusDialog?.prchOrdrId = offrIds;
                                                              //       widget.changeStatusDialog?.assignedVendorId =
                                                              //           int.tryParse(sellerId.toString());

                                                              //       showDialog(
                                                              //           context: context,
                                                              //           builder: (context) {
                                                              //             return widget.changeStatusDialog!;
                                                              //           });
                                                              //     },
                                                              //     text: widget.changeStatusDialog?.statusName
                                                              //             .toUpperCase() ??
                                                              //         "CHANGE STATUS",
                                                              //     //  backgoundColor: AppColors.primaryColorOld,
                                                              //   ),
                                                            ],
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                    separatorBuilder:
                                                        (context, index) {
                                                      return Column(
                                                        children: [
                                                          const SizedBox(
                                                              height: 16),
                                                          Row(
                                                            children: [
                                                              const SizedBox(
                                                                  width: 6),
                                                              Text(
                                                                "End of Summary ${index + 1}",
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 12,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  color: Colors
                                                                      .black,
                                                                ),
                                                              ),
                                                              const Expanded(
                                                                child: Divider(
                                                                  height: 20,
                                                                  color: Colors
                                                                      .black54,
                                                                  thickness: 2,
                                                                  indent: 8,
                                                                  endIndent: 6,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          const SizedBox(
                                                              height: 20),
                                                        ],
                                                      );
                                                    },
                                                  ),
                                                )
                                          : _selectedIndex == 1
                                              ? state.offers.offers?.isEmpty ==
                                                      true
                                                  ? SizedBox(
                                                      height:
                                                          MediaQuery.of(context)
                                                                  .size
                                                                  .height /
                                                              1.5,
                                                      child: Center(
                                                          child: Text(
                                                        "No quotes found",
                                                        style: largeStyle,
                                                      )),
                                                    )
                                                  : ListView.separated(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 2,
                                                          vertical: 0),
                                                      itemCount: state.offers
                                                              .offers?.length ??
                                                          0,
                                                      shrinkWrap: true,
                                                      itemBuilder:
                                                          (context, index) {
                                                        var item = state.offers
                                                            .offers?[index];
                                                        return BuyerCard(
                                                          offerDetails: item!,
                                                          onAccept: (Offer
                                                              offerDetails) {
                                                            // context
                                                            //     .read<
                                                            //         OffersCubit>()
                                                            //     .acceptOffer(
                                                            //         offerDetails
                                                            //             .id
                                                            //             .toString());
                                                          },
                                                          onNegotiate: (Offer
                                                              offerDetails) {
                                                            showDialog(
                                                              context: context,
                                                              builder:
                                                                  (context) {
                                                                return NegotiateDialog(
                                                                  offer:
                                                                      offerDetails,
                                                                  unit: extractUnit(
                                                                      offerDetails),
                                                                );
                                                              },
                                                            );
                                                          },
                                                          showQuantity:
                                                              showQuantity(),
                                                        );
                                                      },
                                                      separatorBuilder:
                                                          (context, index) {
                                                        return const SizedBox(
                                                            height: 12);
                                                      },
                                                    )
                                              :
                                              // Accepted Offers Tab
                                              _selectedIndex == 2
                                                  ? negotiations.isNotEmpty
                                                      ? ListView.separated(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal:
                                                                      2),
                                                          itemCount:
                                                              negotiations
                                                                  .length,
                                                          itemBuilder:
                                                              (context, index) {
                                                            var item =
                                                                negotiations[
                                                                    index];
                                                            return NegotiationCard(
                                                              offerDetails:
                                                                  item,
                                                              onAccept: (Offer
                                                                  offerDetails) {
                                                                // context
                                                                //     .read<
                                                                //         OffersCubit>()
                                                                //     .acceptOffer();
                                                              },
                                                              onNegotiate: (Offer
                                                                  offerDetails) {
                                                                showDialog(
                                                                  context:
                                                                      context,
                                                                  builder:
                                                                      (context) {
                                                                    return NegotiateDialog(
                                                                      offer:
                                                                          offerDetails,
                                                                      unit: extractUnit(
                                                                          offerDetails),
                                                                    );
                                                                  },
                                                                );
                                                              },
                                                              showQuantity:
                                                                  showQuantity(),
                                                            );
                                                          },
                                                          separatorBuilder:
                                                              (context, index) {
                                                            return const SizedBox(
                                                                height: 12);
                                                          },
                                                        )
                                                      :
                                                      // Rejected Offers Tab
                                                      SizedBox(
                                                          height: MediaQuery.of(
                                                                      context)
                                                                  .size
                                                                  .height /
                                                              1.5,
                                                          child: Center(
                                                            child: Text(
                                                              "No negotiations found",
                                                              style: largeStyle,
                                                            ),
                                                          ),
                                                        )
                                                  : SizedBox(
                                                      height:
                                                          MediaQuery.of(context)
                                                                  .size
                                                                  .height /
                                                              1.5,
                                                      child: Center(
                                                        child: Text(
                                                          "No data found",
                                                          style: largeStyle,
                                                        ),
                                                      ),
                                                    ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // if (_selectedIndex == 0)
                  //   Positioned(
                  //     bottom: 16,
                  //     left: 16,
                  //     right: 16,
                  //     child: BaiButton(
                  //       onTap: () {
                  //         var offrIds = OrderStorage.getOrderPairs()
                  //             .map((e) => int.parse(e.prchOrdrOffrId))
                  //             .toList();
                  //         var req = ApprovePoReq(prchOrdrOfferIds: offrIds);
                  //         context.read<OffersCubit>().acceptOffer(req);
                  //       },
                  //       text: "SELECT VENDORS",
                  //     ),
                  //   ),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  String extractUnit(Offer item) {
    String input = "Unit";

    if ((widget.item.mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement")) {
      int id = 0;
      if (item.variant1OptionGroupName?.toLowerCase() == "size") {
        id = 1;
      } else if (item.variant2OptionGroupName?.toLowerCase() == "size") {
        id = 2;
      } else if (item.variant3OptionGroupName?.toLowerCase() == "size") {
        id = 3;
      }

      switch (id) {
        case 1:
          input = item.variant1OptionName ?? "";
          break;
        case 2:
          input = item.variant2OptionName ?? "";
          break;
        case 3:
          input = item.variant3OptionName ?? "";
          break;
        default:
          input = "Unit";
      }

      List<String> parts = input.split(' - ');
      if (parts.isNotEmpty) {
        List<String> lastPart = parts.last.split(' ');
        return lastPart.isNotEmpty ? lastPart.last : '';
      }
    } else {
      return widget.item.optionName ?? "Unit";
    }

    return 'Unit';
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Quote Summary Details:\n\n"
                    "1. Header Information:\n"
                    "• Buyer Name - Company requesting the materials\n"
                    "• Site Details\n"
                    "• MR Number & Date - Material request reference\n"
                    "• Vendor Details - Contact info with click-to-call\n"
                    "• Quotation Details\n\n"
                    "2. Product Information:\n"
                    "• Product Name & Specifications\n"
                    "• Quantity and Units\n"
                    "• Brand Selection - Compare different brands\n"
                    "• Unit Price - Cost per unit\n"
                    "• Total Amount - Complete cost calculation\n\n"
                    "3. Price Breakdown:\n"
                    "• Subtotal - Base cost of materials\n"
                    "• Transportation - Delivery charges\n"
                    "• Discount - Available price reductions\n"
                    "• GST - Applicable taxes\n"
                    "• Final Total - Complete payable amount\n\n"
                    "Note: Red bar indicates if you're viewing the lowest rate combination",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      height: 1.4,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }
}

Widget generateHistoryTable(
  BuildContext context,
  Offer offer, {
  double rowHeight = 40.0,
}) {
  TextStyle style = const TextStyle(
    fontSize: 12.0,
    fontWeight: FontWeight.bold,
  );
  TextStyle headingStyle = const TextStyle(
    fontSize: 13.0,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  // Define the row count based on the history list length
  int rowCount = offer.negotiationHistory?.length ?? 0;

  // Sort the negotiation history by id in descending order (newest first)
  if (offer.negotiationHistory != null &&
      offer.negotiationHistory!.isNotEmpty) {
    offer.negotiationHistory!.sort((a, b) {
      // Handle null id values
      if (a.id == null && b.id == null) {
        return 0;
      } else if (a.id == null) {
        return 1; // null values go to the end
      } else if (b.id == null) {
        return -1; // null values go to the end
      }
      // Sort in descending order (highest ID first, assuming higher ID means newer)
      return b.id!.compareTo(a.id!);
    });
  }

  return rowCount == 0
      ? Container()
      : Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.black, width: 1),
          ),
          child: Table(
            border: const TableBorder(
              horizontalInside: BorderSide(color: Colors.black, width: 1),
              verticalInside: BorderSide(color: Colors.black, width: 1),
            ),
            columnWidths: const {
              0: FlexColumnWidth(2),
              1: FlexColumnWidth(2),
              2: FlexColumnWidth(2),
              3: FlexColumnWidth(2),
              4: FlexColumnWidth(3),
              5: FlexColumnWidth(2),
            },
            children: [
              // Header row
              TableRow(
                decoration: const BoxDecoration(
                  color: AppColors.primaryColorOld,
                ),
                children: [
                  Container(
                    height: rowHeight,
                    alignment: Alignment.center,
                    child: Text("User",
                        textAlign: TextAlign.center, style: headingStyle),
                  ),
                  Container(
                    height: rowHeight,
                    alignment: Alignment.center,
                    child: Text("₹",
                        textAlign: TextAlign.center, style: headingStyle),
                  ),
                  Container(
                    height: rowHeight,
                    alignment: Alignment.center,
                    child: Text("Qty",
                        textAlign: TextAlign.center, style: headingStyle),
                  ),
                  Container(
                    height: rowHeight,
                    alignment: Alignment.center,
                    child: Text("₹/Unit",
                        textAlign: TextAlign.center, style: headingStyle),
                  ),
                  Container(
                    height: rowHeight,
                    alignment: Alignment.center,
                    child: Text("Cmnt",
                        textAlign: TextAlign.center, style: headingStyle),
                  ),
                  Container(
                    height: rowHeight,
                    alignment: Alignment.center,
                    child: Text("Open",
                        textAlign: TextAlign.center, style: headingStyle),
                  ),
                ],
              ),
              // Data rows based on negotiation history (already sorted)
              for (NegotiationHistory negotiation
                  in offer.negotiationHistory!) ...[
                TableRow(
                  decoration: BoxDecoration(
                    color: negotiation.statusCode == "ACPT"
                        ? Colors.green.shade100
                        : negotiation.customerType == "BUYR"
                            ? Colors.white
                            : AppColors.primaryColor,
                  ),
                  children: [
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(
                        '${negotiation.companyName}',
                        textAlign: TextAlign.center,
                        style: negotiation.customerType == "BUYR"
                            ? style
                            : style.copyWith(color: Colors.white),
                        maxLines: 2,
                      ),
                    ),
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(
                          formatToIndianRupee(
                              negotiation.offerPrice.toString()),
                          textAlign: TextAlign.center,
                          style: negotiation.customerType == "BUYR"
                              ? style
                              : style.copyWith(color: Colors.white)),
                    ),
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(negotiation.quantity?.toString() ?? "N/A",
                          textAlign: TextAlign.center,
                          style: negotiation.customerType == "BUYR"
                              ? style
                              : style.copyWith(color: Colors.white)),
                    ),
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(
                          negotiation.quantity != null &&
                                  negotiation.quantity! > 0
                              ? ((negotiation.offerPrice ?? 0) /
                                      (negotiation.quantity ?? 0))
                                  .toStringAsFixed(2)
                              : "N/A",
                          textAlign: TextAlign.center,
                          style: negotiation.customerType == "BUYR"
                              ? style
                              : style.copyWith(color: Colors.white)),
                    ),
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(
                        negotiation.remarks?.isEmpty == true
                            ? "N/A"
                            : negotiation.remarks ?? "N/A",
                        textAlign: TextAlign.center,
                        style: negotiation.customerType == "BUYR"
                            ? style
                            : style.copyWith(color: Colors.white),
                        maxLines: 2,
                      ),
                    ),
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: SizedBox(
                        // height: rowHeight / 1.5,
                        // width: 60,
                        child: IconButton(
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                // Check if this is the last item in the negotiation history
                                final isLatestItem =
                                    offer.negotiationHistory!.first.id ==
                                        negotiation.id;
                                return QuotesDialog(
                                  negotiationHistory: negotiation,
                                  offer: offer,
                                  isLatestItem: isLatestItem,
                                );
                              },
                            );
                          },
                          icon: Icon(
                            Icons.folder_copy_outlined,
                            color: negotiation.customerType == "BUYR"
                                ? Colors.black
                                : Colors.white,
                            size: 18,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
}

// Custom TabItem widget
// This widget is used in the OfferPage and NotificationsPage
// to display the tabs at the top of the page
class TabItem extends StatelessWidget {
  // The text to display in the tab
  final String text;
  // The prefix to display before the text
  final String? prefix;
  // The suffix to display after the text
  final String? suffix;
  // The index of the tab
  final int index;
  // The index of the selected tab
  final int selectedIndex;
  // The color of the selected tab
  final Color selectedColor;
  // The color of the unselected tab
  final Color unselectedColor;
  // The color of the text in the selected tab
  final Color selectedTextColor;
  // The color of the text in the unselected tab
  final Color unselectedTextColor;
  // The function to call when the tab is tapped
  final Function onTap;
  // The width of the tab
  final double width;
  // The font size of the text in the tab
  final double? fontSize;

  const TabItem({
    Key? key,
    required this.text,
    this.prefix,
    this.suffix,
    required this.index,
    required this.selectedColor,
    required this.unselectedColor,
    required this.selectedTextColor,
    required this.unselectedTextColor,
    required this.selectedIndex,
    required this.onTap,
    required this.width,
    this.fontSize = 14,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap(index);
      },
      child: Container(
        margin: const EdgeInsets.only(top: 1, bottom: 1, left: 0.1, right: 0.1),
        child: Material(
          elevation: 2,
          clipBehavior: Clip.hardEdge,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
          ),
          child: Container(
            height: double.infinity,
            width: width,
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
              color: index == selectedIndex ? selectedColor : unselectedColor,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Display the prefix if it is not null
                if (prefix != null) ...[
                  const SizedBox(width: 2),
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.green.shade600,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        prefix!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                ],
                // Display the text
                Text(
                  text,
                  style: TextStyle(
                    fontSize: fontSize,
                    fontWeight: FontWeight.bold,
                    color: index == selectedIndex
                        ? selectedTextColor
                        : unselectedTextColor,
                  ),
                ),
                // Display the suffix if it is not null
                if (suffix != null) ...[
                  const SizedBox(width: 6),
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.cyan.shade600,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        suffix!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 2),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
/////////////new table

class InvitationTable extends StatelessWidget {
  final InviteStatusRes? invite;
  final double rowHeight;
  final Function(VendorDetail) onRemind;

  const InvitationTable({
    Key? key,
    this.rowHeight = 40.0,
    required this.onRemind,
    this.invite,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    TextStyle style = const TextStyle(
      fontSize: 12.0,
      fontWeight: FontWeight.bold,
    );
    TextStyle headingStyle = const TextStyle(
      fontSize: 13.0,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    );

    List<VendorDetail>? vendorDetails = invite?.vendorDetails ?? [];

    return vendorDetails.isEmpty
        ? SizedBox(
            height: 480,
            child: Center(
              child: Text(
                "No vendors found",
                style: style.copyWith(fontSize: 16),
              ),
            ),
          )
        : Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.black,
                width: 1,
              ),
            ),
            margin: const EdgeInsets.all(0),
            child: Table(
              border: const TableBorder(
                horizontalInside: BorderSide(
                  color: Colors.black,
                  width: 1,
                ),
                verticalInside: BorderSide(
                  color: Colors.black,
                  width: 1,
                ),
              ),
              columnWidths: const {
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(1),
                2: FlexColumnWidth(2),
                3: FlexColumnWidth(2),
              },
              children: [
                TableRow(
                  decoration:
                      const BoxDecoration(color: AppColors.primaryColorOld),
                  children: [
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(
                        "Vendor",
                        textAlign: TextAlign.center,
                        style: headingStyle,
                      ),
                    ),
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(
                        "Views",
                        textAlign: TextAlign.center,
                        style: headingStyle,
                      ),
                    ),
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(
                        "Offer Status",
                        textAlign: TextAlign.center,
                        style: headingStyle,
                      ),
                    ),
                    Container(
                      height: rowHeight,
                      alignment: Alignment.center,
                      child: Text(
                        "Action",
                        textAlign: TextAlign.center,
                        style: headingStyle,
                      ),
                    ),
                  ],
                ),
                for (var vendor in vendorDetails)
                  TableRow(
                    children: [
                      Container(
                        height: rowHeight,
                        alignment: Alignment.center,
                        child: Text(
                          vendor.vendorName ?? 'N/A',
                          textAlign: TextAlign.center,
                          style: style,
                        ),
                      ),
                      Container(
                        height: rowHeight,
                        alignment: Alignment.center,
                        child: Text(
                          vendor.noOfViews?.toString() ?? '0',
                          textAlign: TextAlign.center,
                          style: style,
                        ),
                      ),
                      Container(
                        height: rowHeight,
                        alignment: Alignment.center,
                        child: Text(
                          vendor.status ?? 'Pending',
                          textAlign: TextAlign.center,
                          style: style,
                        ),
                      ),
                      Container(
                        height: rowHeight,
                        alignment: Alignment.center,
                        child: SizedBox(
                          height: rowHeight / 1.5,
                          width: 70,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  AppColors.primaryColorOld, // Button color
                            ),
                            onPressed: vendor.isRemind == true
                                ? null
                                : () {
                                    onRemind(vendor);
                                  },
                            child: const Text(
                              'Remind',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          );
  }
}

class OrderPair {
  final String prchOrdrId;
  final String sellerId;
  String prchOrdrOffrId;

  OrderPair(this.prchOrdrId, this.sellerId, this.prchOrdrOffrId);
}

class OrderStorage {
  static final List<OrderPair> _orderPairs = [];

  static void addOrUpdateOrderPair(
      String prchOrdrId, String sellerId, String prchOrdrOffrId) {
    for (var pair in _orderPairs) {
      if (pair.prchOrdrId == prchOrdrId && pair.sellerId == sellerId) {
        pair.prchOrdrOffrId = prchOrdrOffrId;
        return;
      }
    }
    _orderPairs.add(OrderPair(prchOrdrId, sellerId, prchOrdrOffrId));
  }

  static List<OrderPair> getOrderPairs() {
    return List.unmodifiable(_orderPairs);
  }

  static void clearOrderPairs() {
    _orderPairs.clear();
  }

  static List<int> getOfferIds(String sellerId) {
    return List.unmodifiable(_orderPairs
        .where((e) => e.sellerId == sellerId.toString())
        .map((e) => int.parse(e.prchOrdrOffrId))
        .toList());
  }
}
