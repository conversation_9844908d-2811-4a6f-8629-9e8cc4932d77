import 'dart:ui';

import 'package:connectone/bai_blocs/buyer_reg/cubit/buyer_registration_cubit.dart';
import 'package:connectone/bai_models/register_req.dart';
import 'package:connectone/bai_screens/registration_buyer.dart';
import 'package:connectone/bai_screens/registration_otp.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/document_list_widget.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';

class RegistrationBuyerSummaryScreen extends StatefulWidget {
  const RegistrationBuyerSummaryScreen({Key? key, required this.data})
      : super(key: key);

  final BuyerRegData data;

  @override
  State<RegistrationBuyerSummaryScreen> createState() =>
      _RegistrationBuyerScreenState();
}

class _RegistrationBuyerScreenState
    extends State<RegistrationBuyerSummaryScreen> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _loginIdController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _websiteController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _businessEmailController =
      TextEditingController();
  final TextEditingController _businessPhoneController =
      TextEditingController();

  bool checkboxValue = false;

  @override
  void initState() {
    super.initState();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Buyer Registration Summary!\n\nHere's what you need to know:\n\n1. Review your business details at the top\n\n2. Fill in your login credentials carefully\n\n3. Create a strong password and confirm it\n\n4. Make sure to read and accept all terms and policies\n\n5. Double-check all information before registering\n\nClick Register when you're ready to complete your account setup.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override

  /// Builds the registration summary screen for buyers.
  ///
  /// This screen displays the summary of the buyer's registration details.
  /// It also contains a form with fields for the buyer to enter their
  /// login ID, email, password, and confirm password.
  /// The form also includes a checkbox to accept the terms and conditions
  /// and a button to submit the registration.
  Widget build(BuildContext context) {
    return AppLoader(
      child: Scaffold(
        backgroundColor: AppColors.primaryColor,
        appBar: AppBar(
          title: const Text('Registration - Buyer'),
          backgroundColor: AppColors.primaryColor,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          actions: [
            InfoHelp(
              key: key1,
              onTap: () {
                showTutorial();
              },
            )
          ],
        ),
        body: BlocConsumer<BuyerRegistrationCubit, BuyerRegistrationState>(
          listener: (context, state) {
            if (state is BuyerRegistrationError) {
              alert(state.message.contains("already registered").toString());
              if (state.message.contains("already registered")) {
                Get.off(RegistrationOTP(
                  user: _emailController.text,
                  isSeller: true,
                  sendOtpInitially: true,
                ));
              } else {
                properAlert(state.message);
              }
            }
            if (state is BuyerRegistrationSuccess) {
              Get.off(RegistrationOTP(
                user: _emailController.text,
                isSeller: false,
                sendOtpInitially: false,
              ));
            }
          },
          builder: (context, state) {
            (state is BuyerRegistrationLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: ListView(
                  children: <Widget>[
                    _buildSummary(),
                    const SizedBox(height: 32),
                    RegistrationRichTextField(
                      labelText: 'Login ID (Phone)',
                      controller: _loginIdController,
                      maxLength: 10,
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Login ID is required';
                        }
                        if (value.length != 10) {
                          return 'Login ID must be 10 digits';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    RegistrationRichTextField(
                      labelText: 'User Name',
                      controller: _nameController,
                      keyboardType: TextInputType.name,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Name is required';
                        }
                        return null;
                      },
                    ),
                    if (widget.data.designation == "Admin")
                      const SizedBox(height: 20),
                    if (widget.data.designation == "Admin")
                      RegistrationTextField(
                          labelText: 'Website',
                          controller: _websiteController,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value != "") {
                              const urlPattern =
                                  r'^(https?:\/\/)?(www\.)?([a-zA-Z0-9\-]+\.)+[a-zA-Z]{2,6}(\/\S*)?$';
                              final result =
                                  RegExp(urlPattern).hasMatch(value!);
                              if (!result) {
                                return 'Please enter a valid website URL';
                              }
                            }
                            return null;
                          }),
                    if (widget.data.designation == "Admin")
                      const SizedBox(height: 20),
                    if (widget.data.designation == "Admin")
                      RegistrationRichTextField(
                          labelText: 'Business Email',
                          controller: _businessEmailController,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Email is required';
                            }
                            if (!GetUtils.isEmail(value)) {
                              return 'Invalid email address';
                            }
                            return null;
                          }),
                    if (widget.data.designation == "Admin")
                      const SizedBox(height: 20),
                    if (widget.data.designation == "Admin")
                      RegistrationTextField(
                          labelText: 'Business Phone',
                          controller: _businessPhoneController,
                          maxLength: 10,
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value != '' && value!.length != 10) {
                              return 'Phone number must be 10 digits';
                            }
                            return null;
                          }),
                    if (widget.data.designation == "Admin")
                      const SizedBox(height: 20),
                    if (widget.data.designation == "Admin")
                      RegistrationRichTextField(
                        labelText: 'Email',
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Email is required';
                          }
                          if (!GetUtils.isEmail(value)) {
                            return 'Invalid email address';
                          }
                          return null;
                        },
                      ),
                    const SizedBox(height: 20),
                    RegistrationPwTextField(
                      labelText: 'Password',
                      controller: _passwordController,
                      obscureText: true,
                      keyboardType: TextInputType.name,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Password is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    RegistrationPwTextField(
                      labelText: 'Confirm Password',
                      controller: _confirmPasswordController,
                      obscureText: true,
                      keyboardType: TextInputType.visiblePassword,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Confirm Password is required';
                        }
                        if (value != _passwordController.text) {
                          return 'Passwords do not match';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 32),
                    DocumentListWidgetBuyer(onAllChecked: (val) {
                      setState(() {
                        checkboxValue = val;
                      });
                    }),
                    const SizedBox(height: 32),
                    ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          if (checkboxValue == false) {
                            alert(
                                "Please accept the privacy policy, terms and conditions, refund & cancellation policies.");
                            return;
                          }
                          var nc = NetworkController();
                          var data = widget.data;

                          var req = RegisterReq(
                            name: _nameController.text,
                            vendorName: data.vendor,
                            vendorId: data.vendorId,
                            email: _emailController.text.isNotEmpty
                                ? _emailController.text
                                : generateRandomEmail(),
                            password: _passwordController.text,
                            termsAndConditions: true,
                            customerType: "BUYR",
                            gstNumber: data.gst,
                            phone: _loginIdController.text,
                            countryCode: "+91",
                            clientId: nc.organisationData?.appClientAndroid,
                            areaOfSupply: [],
                            baiMember: null,
                            typeOfGstFilling: "monthly",
                            addressLineOne: data.address1,
                            addressLineTwo: data.address2,
                            website: _websiteController.text.trim(),
                            // businessEmail: _businessEmailController.text.trim(),
                            // businessPhone: _businessPhoneController.text.trim(),
                            state: "",
                            zip: "",
                            city: "",
                            shippingAddress: ShippingAddress(
                              addressLine1: data.address1,
                              addressLine2: data.address2,
                              city: "",
                              state: "",
                              pincode: "",
                              country: "",
                              latitude: 0,
                              longitude: 0,
                            ),
                            loginType: "PHONE",
                            location: data.district,
                            natureOfBusiness: data.natureOfBusiness.isEmpty
                                ? null
                                : data.natureOfBusiness.first,
                            designation: data.designation,
                          );
                          context
                              .read<BuyerRegistrationCubit>()
                              .registerBuyer(req);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: AppColors.primaryColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 40, vertical: 20),
                        textStyle: const TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      child: const Text("Register"),
                    ),
                    const SizedBox(height: 12),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String generateRandomEmail() {
    String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    // Random random = Random();
    // int randomNumber = random.nextInt(100000);
    String email = 'user_$<EMAIL>';
    _emailController.text = email;
    return email;
  }

  Widget _buildSummary() {
    final List<String> summaryLines = [];

    // Only add lines if the string is not empty
    if (widget.data.district.isNotEmpty) {
      summaryLines.add(widget.data.district);
    }
    if (widget.data.vendor.isNotEmpty) {
      summaryLines.add(widget.data.vendor);
    }
    if (widget.data.natureOfBusiness.isNotEmpty) {
      summaryLines.add(widget.data.natureOfBusiness.join(', '));
    }
    // if (widget.data.designation.isNotEmpty) {
    //   summaryLines.add(widget.data.designation);
    // }
    if (widget.data.gst.isNotEmpty) {
      summaryLines.add(widget.data.gst);
    }
    if (widget.data.address1.trim().isNotEmpty) {
      summaryLines.add(widget.data.address1);
    }
    if (widget.data.address2.trim().isNotEmpty) {
      summaryLines.add(widget.data.address2);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          summaryLines.join('\n'),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          widget.data.designation,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            backgroundColor: AppColors.green,
          ),
        ),
      ],
    );
  }
}

class RegistrationPwTextField extends StatefulWidget {
  final String labelText;
  final TextEditingController controller;
  final bool obscureText;
  final int? maxLength;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;

  const RegistrationPwTextField({
    Key? key,
    required this.labelText,
    required this.controller,
    required this.keyboardType,
    this.maxLength,
    this.obscureText = false,
    this.validator,
  }) : super(key: key);

  @override
  State<RegistrationPwTextField> createState() =>
      _RegistrationPwTextFieldState();
}

class _RegistrationPwTextFieldState extends State<RegistrationPwTextField> {
  bool _isPasswordVisible = false;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      obscureText: !_isPasswordVisible,
      style: const TextStyle(color: Colors.white),
      maxLength: widget.maxLength,
      keyboardType: widget.keyboardType,
      validator: widget.validator,
      decoration: InputDecoration(
        label: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: widget.labelText,
                style: const TextStyle(color: Colors.white),
              ),
              const TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
        helperStyle: const TextStyle(color: Colors.white),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        isDense: true,
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: Colors.white,
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
      ),
    );
  }
}
