import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:connectone/bai_blocs/notification/notification_data_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_screens/edit_assign_approve.dart';
import 'package:connectone/bai_screens/history_offers_more.dart';
import 'package:connectone/core/bai_widgets/user_profile_dialog.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/push_notifications/notification_controller.dart';
import 'package:connectone/core/utils/circular_progress.dart';
import 'package:connectone/old_screens/offline_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';
import 'package:share_plus/share_plus.dart';
import 'package:connectone/core/utils/extensions.dart';
import '../core/utils/time_utils.dart';
import '../core/utils/colors.dart';
import '../core/utils/tools.dart';
import '../old_blocs/offline_stocks/offline_stocks_bloc.dart';

class SingleMRScreen extends StatefulWidget {
  const SingleMRScreen({
    Key? key,
    required this.notificationData,
    this.mvtPrchOrdrHistoryId,
    required this.isNotify,
  }) : super(key: key);

  final NotificationData notificationData;
  final String? mvtPrchOrdrHistoryId;
  final bool? isNotify;

  @override
  State<SingleMRScreen> createState() => _SingleMRScreenState();
}

class _SingleMRScreenState extends State<SingleMRScreen> {
  // late StreamSubscription streamSubscription;
  var isOpen = false;

  @override
  void initState() {
    super.initState();
    var notificationId = widget.notificationData.id;

    if (notificationId != null || widget.mvtPrchOrdrHistoryId != null) {
      context.read<NotificationDataCubit>().loadNotificationData(
            notificationId.toString(),
            mvtPrchOrdrHistoryId: widget.mvtPrchOrdrHistoryId,
          );
    }
  }

  int getType() {
    if (widget.notificationData.code == "PO_01") {
      return 1;
    } else {
      return 2;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override

  /// Builds the UI for the notifications screen
  ///
  /// This method is the root of the widget tree for the notifications screen.
  /// It uses the BlocConsumer widget to consume the NotificationDataCubit and
  /// build the UI based on the state of the cubit.
  ///
  /// The UI is built in the following way:
  /// - If the state is NotificationDataLoading, it displays a progress indicator.
  /// - If the state is NotificationDataLoaded, it displays the first content item
  ///   in the list of content items. If the list is empty, it displays a message
  ///   indicating that no notification is available.
  ///
  /// The `isOpen` parameter is used to determine whether the notification item
  /// should be displayed as open or closed. The `getType` method is used to determine
  /// the type of the notification item based on the code of the notification data.
  Widget build(BuildContext context) {
    var isEdited = widget.mvtPrchOrdrHistoryId != null;
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        title: Text(widget.notificationData.subject),
      ),
      body: BlocListener<OfflineMainBloc, OfflineMainState>(
        listener: (context, state) {
          if (state is OfflineStocksRefresh) {
            context.read<NotificationDataCubit>().loadNotificationData(
                  widget.notificationData.id.toString(),
                  mvtPrchOrdrHistoryId: widget.mvtPrchOrdrHistoryId,
                );
          }
        },
        child: BlocConsumer<NotificationDataCubit, NotificationDataState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is NotificationDataLoading) {
              // Display a progress indicator while the data is loading
              return Center(child: progressIndicator);
            }

            if (state is NotificationDataLoaded) {
              // Get the notification data from the state
              var notificationData = state.notification;
              if (notificationData.content != null &&
                  notificationData.content!.isNotEmpty) {
                // Display the first content item in the list of content items
                return NotificationItem(
                  isOpen,
                  widget.isNotify ?? false,
                  notificationData.content![0],
                  widget.notificationData,
                  type: getType(),
                  isEdited: isEdited,
                );
              } else {
                // Display a message if the list of content items is empty
                return const SizedBox(
                  height: double.infinity,
                  width: double.infinity,
                  child: Center(
                    child: Text(
                      'No data available',
                    ),
                  ), // Display a message or return an empty container
                );
              }
            }
            // Return an empty container if the state is not one of the above
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}

class NotificationItem extends StatefulWidget {
  final bool isOpen;
  final bool isNotify;

  final Content currentItem;
  final NotificationData notificationData;
  final int type;
  final bool isEdited;

  const NotificationItem(
    this.isOpen,
    this.isNotify,
    this.currentItem,
    this.notificationData, {
    Key? key,
    required this.type,
    this.isEdited = false,
  }) : super(key: key);

  @override
  State<NotificationItem> createState() => _NotificationItemState();
}

class _NotificationItemState extends State<NotificationItem> {
  var isOpened = false;

  List<String> urls = [];
  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];

  bool playing = false;

  final player = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _setArrays();
  }

  void _setArrays() {
    final item = widget.currentItem;

    urls = [
      if (item.image1 != null) item.image1!,
      if (item.image2 != null) item.image2!,
      if (item.image3 != null) item.image3!,
      if (item.image4 != null) item.image4!,
      if (item.image5 != null) item.image5!,
    ];

    for (var url in urls) {
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  bool showQuantity() {
    return !(widget.currentItem.mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement");
  }

  @override
  Widget build(BuildContext context) {
    print('--------widget.currentItem.quantity ${widget.currentItem.quantity}');
    return GestureDetector(
      onTap: () {
        // context.read<OfflineMainBloc>().add(ItemSelected(widget.currentItem.prchOrdrId?.toInt() ?? 0));
      },
      child: Stack(children: [
        ListView(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
              child: Center(
                child: Text(
                  widget.notificationData.content,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            Container(
              foregroundDecoration: RotatedCornerDecoration.withColor(
                badgeSize: const Size(28, 28),
                badgeCornerRadius: const Radius.circular(8),
                badgePosition: BadgePosition.topEnd,
                color: widget.currentItem.mrStatusButtonColor != null
                    ? getColorFromHex(widget.currentItem.mrStatusButtonColor!)
                    : getCategoryColor(widget.currentItem.statusCd ?? "") ??
                        Colors.transparent,
                textSpan: null,
              ),
              margin: const EdgeInsets.fromLTRB(8, 8, 8, 120),
              child: Card(
                // color: Highlight ? Colors.lightBlueAccent : Colors.lightGreenAccent,
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(12.0),
                    ),
                    side: true
                        ? BorderSide(color: AppColors.green, width: 2)
                        : BorderSide(color: Colors.transparent, width: 2)),
                elevation: 4.0,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 56,
                            width: 56,
                            child: Card(
                              shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(6),
                                ),
                              ),
                              clipBehavior: Clip.hardEdge,
                              elevation: 2.0,
                              child: GestureDetector(
                                onTap: () {
                                  showImageDialog(context, images[0]);
                                },
                                child: Center(
                                  child: images.isNotEmpty
                                      ? Image.network(
                                          images[0],
                                          fit: BoxFit.fill,
                                        )
                                      : Image.asset(
                                          'assets/images/no_image_available.jpeg',
                                          fit: BoxFit.fill,
                                        ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: kIsWeb
                                    ? 400
                                    : MediaQuery.of(context).size.width - 100,
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        widget.currentItem.mvtItemName
                                                ?.toString() ??
                                            "N/A",
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16),
                                      ),
                                    ),
                                    const SizedBox(width: 6),
                                    // RatingBarIndicator(
                                    //   rating: widget.currentItem.vendorRating?.toDouble() ?? 0,
                                    //   itemBuilder: (context, index) => const Icon(
                                    //     Icons.star,
                                    //     color: Colors.amber,
                                    //   ),
                                    //   itemCount: 5,
                                    //   itemSize: 15.0,
                                    //   unratedColor: Colors.grey,
                                    //   direction: Axis.horizontal,
                                    // ),
                                    // const SizedBox(width: 6),
                                    // const Text(
                                    //   "Open",
                                    //   style: TextStyle(fontWeight: FontWeight.bold),
                                    // ),
                                    const SizedBox(width: 16),

                                    IconButton(
                                      icon: Icon(
                                        Icons.ios_share,
                                        color: AppColors.primaryColor,
                                      ),
                                      onPressed: () async {
                                        final productUrl =
                                            'https://baistore.cochq.au/mr?id=${widget.currentItem.prchOrdrId}';
                                        final message =
                                            'Discover this product (ID: ${widget.currentItem.orderGroupName}) at $productUrl on the bai Store App.';
                                        await Share.share(message);
                                      },
                                      padding: const EdgeInsets.only(bottom: 6),
                                      constraints: const BoxConstraints(),
                                    ),
                                    const SizedBox(width: 2),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 6),
                              SizedBox(
                                width: kIsWeb
                                    ? 400
                                    : MediaQuery.of(context).size.width - 100,
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        widget.currentItem.orderGroupName ?? "",
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14.0,
                                          // color: AppColors.primaryColor,
                                        ),
                                        maxLines: 2,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // SizedBox(
                              //   width: MediaQuery.of(context).size.width - 100,
                              //   child: Row(
                              //     children: [
                              //       Expanded(
                              //         child: Text(
                              //           widget.currentItem.statusName ?? "",
                              //           style: const TextStyle(
                              //             fontWeight: FontWeight.bold,
                              //             fontSize: 14.0,
                              //             color: Colors.black,
                              //           ),
                              //           maxLines: 2,
                              //         ),
                              //       ),
                              //     ],
                              //   ),
                              // ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 4,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ////
                          isBuyer()
                              ? Column(
                                  children: [
                                    EnquiryKeyValue(
                                        key1: "Created By:",
                                        value: widget.currentItem.createdBy ??
                                            "N/A"),
                                    const SizedBox(height: 4),
                                    EnquiryKeyValue(
                                        key1: "Site Name:",
                                        value: widget.currentItem.projectName ??
                                            "N/A"),
                                    const SizedBox(height: 4),
                                    EnquiryKeyValue(
                                        key1: "Split Name:",
                                        value: widget.currentItem
                                                .prchOrdrSplitName ??
                                            "N/A"),
                                    const SizedBox(height: 4),
                                    EnquiryKeyValue(
                                        key1: "Category:",
                                        value: widget.currentItem
                                                .cappCategoriesName ??
                                            "N/A"),
                                    if (showQuantity())
                                      const SizedBox(height: 4),
                                    if (showQuantity())
                                      EnquiryKeyValue(
                                          key1: "Quantity:",
                                          value: widget.currentItem.quantity
                                                      ?.toInt() ==
                                                  -1
                                              ? 'Multiple'
                                              : "${widget.currentItem.quantity?.toStringAsFixed(0)} ${widget.currentItem.optionName ?? ""}" ??
                                                  "N/A"),
                                    // const SizedBox(height: 4),
                                    // EnquiryKeyValue(key1: "Product Name:", value: widget.currentItem.mvtItemName ?? "N/A"),
                                    const SizedBox(height: 4),
                                    EnquiryKeyValue(
                                      key1: "Status:",
                                      value: widget.currentItem.statusName ??
                                          "N/A",
                                      bold: true,
                                    ),
                                  ],
                                )
                              : Column(
                                  children: [
                                    // const SizedBox(height: 4),
                                    // EnquiryKeyValue(key1: "Product Name:", value: widget.currentItem.mvtItemName ?? "N/A"),
                                    if (showQuantity())
                                      const SizedBox(height: 4),
                                    if (showQuantity())
                                      EnquiryKeyValue(
                                        key1: "Quantity:",
                                        value: widget.currentItem.quantity
                                                    ?.toInt() ==
                                                -1
                                            ? 'Multiple'
                                            : "${widget.currentItem.quantity?.toStringAsFixed(0)} ${widget.currentItem.optionName ?? ""}" ??
                                                "N/A",
                                      ),
                                    const SizedBox(height: 4),
                                    EnquiryKeyValue(
                                      key1: "Category:",
                                      value: widget
                                              .currentItem.cappCategoriesName ??
                                          "N/A",
                                    ),
                                    const SizedBox(height: 4),

                                    EnquiryKeyValue(
                                      key1: "Status:",
                                      value: widget.currentItem.statusName ??
                                          "N/A",
                                      bold: true,
                                    ),
                                    const SizedBox(height: 4),
                                    // InkWell(
                                    //   onTap: () {
                                    //     showDialog(
                                    //       context: context,
                                    //       builder: (BuildContext context) {
                                    //         return const UserProfileDialog();
                                    //       },
                                    //     );
                                    //   },
                                    //   child: EnquiryKeyValue(
                                    //       key1: "Buyer Name:",
                                    //       value: (widget.mode == 2 ? widget.currentItem.buyerCustomerName : widget.currentItem.customerName) ?? "N/A"),
                                    // ),
                                    // const SizedBox(height: 4),
                                    InkWell(
                                        onTap: () {
                                          showDialog(
                                            context: context,
                                            builder: (BuildContext context) {
                                              return UserProfileDialog(
                                                vendorId:
                                                    widget.currentItem.vendorId,
                                                prchOrdrId: widget
                                                    .currentItem.prchOrdrId,
                                              );
                                            },
                                          );
                                        },
                                        child: EnquiryKeyValue(
                                          key1: "Buyer Company:",
                                          value:
                                              widget.currentItem.vendorName ??
                                                  "N/A",
                                        )),
                                    const SizedBox(height: 4),
                                    InkWell(
                                      onTap: () {
                                        makePhoneCall((widget
                                                .currentItem.customerPhone) ??
                                            "");
                                      },
                                      child: EnquiryKeyValue(
                                        key1: "Buyer Phone:",
                                        value: (widget
                                                .currentItem.customerPhone) ??
                                            "N/A",
                                      ),
                                    ),
                                  ],
                                ),
                          const SizedBox(height: 4),
                          EnquiryKeyValue(
                            key1: "Created On:",
                            value: widget.currentItem.createdAt != null
                                ? widget.currentItem.createdAt!.toCreatedOn()
                                : '',
                          ),
                          const SizedBox(height: 4),
                          EnquiryKeyValue(
                            key1: "Delivery On:",
                            value: widget.currentItem.deliveryDate != null
                                ? widget.currentItem.deliveryDate!
                                    .toDeliveryOn()
                                : '',
                          ),

                          const SizedBox(height: 4),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 1),
                            child: Row(
                              children: [
                                const Expanded(
                                  flex: 2,
                                  child: Text(
                                    "Attachments:",
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  flex: 3,
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Row(
                                      children: [
                                        if (images.isNotEmpty)
                                          Icon(
                                            Icons.image_outlined,
                                            size: 16,
                                            color: AppColors.primaryColor,
                                          ),
                                        if (audios.isNotEmpty)
                                          const SizedBox(width: 6),
                                        if (audios.isNotEmpty)
                                          Icon(
                                            Icons.play_arrow_outlined,
                                            size: 20,
                                            color: AppColors.primaryColor,
                                          ),
                                        if (files.isNotEmpty)
                                          const SizedBox(width: 6),
                                        if (files.isNotEmpty)
                                          Icon(
                                            Icons.file_copy_outlined,
                                            size: 16,
                                            color: AppColors.primaryColor,
                                          ),
                                        if (images.isEmpty &&
                                            audios.isEmpty &&
                                            files.isEmpty)
                                          const Text(
                                            "N/A",
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          if (widget.currentItem.variant1Options != null &&
                              widget.currentItem.variant1OptionGroupName !=
                                  null &&
                              widget.currentItem.variant1OptionGroupName!
                                  .isNotEmpty) ...[
                            const SizedBox(height: 4),
                            EnquiryKeyValue(
                              key1:
                                  "${widget.currentItem.variant1OptionGroupName}:",
                              value: widget.currentItem.variant1Options
                                      ?.addSpaceAfterCommas() ??
                                  "N/A",
                            ),
                          ],
                          if (widget.currentItem.variant2Options != null &&
                              widget.currentItem.variant2OptionGroupName !=
                                  null &&
                              widget.currentItem.variant2OptionGroupName !=
                                  widget
                                      .currentItem.variant1OptionGroupName) ...[
                            const SizedBox(height: 4),
                            EnquiryKeyValue(
                              key1:
                                  "${widget.currentItem.variant2OptionGroupName}:",
                              value: widget.currentItem.variant2Options
                                      ?.addSpaceAfterCommas() ??
                                  "N/A",
                            ),
                          ],
                          if (widget.currentItem.variant3Options != null &&
                              widget.currentItem.variant3OptionGroupName !=
                                  null &&
                              widget.currentItem.variant3OptionGroupName !=
                                  widget.currentItem.variant1OptionGroupName &&
                              widget.currentItem.variant3OptionGroupName !=
                                  widget
                                      .currentItem.variant2OptionGroupName) ...[
                            const SizedBox(height: 4),
                            EnquiryKeyValue(
                              key1:
                                  "${widget.currentItem.variant3OptionGroupName}:",
                              value: widget.currentItem.variant3Options
                                      ?.addSpaceAfterCommas() ??
                                  "N/A",
                            ),
                          ],
                          const SizedBox(height: 4),

                          // if (widget.currentItem.variants != null &&
                          //     widget.currentItem.variants?[0].variant1OptionName !=
                          //         null) ...[
                          //   const SizedBox(height: 4),
                          // EnquiryKeyValue(
                          //   key1:
                          //       "${widget.currentItem.variants?[0].variant1OptionId}:",
                          //   value: widget.currentItem.variants?[0].variant1OptionName
                          //            ??
                          //       "N/A",
                          // ),
                          //    Text(
                          //                 "${widget.currentItem.variants?[0].variant1OptionName}"??"N/A",
                          //                 style: TextStyle(
                          //                     fontWeight: FontWeight.bold),
                          //               ),
                          // ],
                          //                if (widget.currentItem.variants != null &&
                          //     widget.currentItem.variants?[1].variant2OptionName !=
                          //         null) ...[
                          //   const SizedBox(height: 4),
                          //   // EnquiryKeyValue(
                          //   //   key1:
                          //   //       "${widget.currentItem.variants?[1].variant2OptionId}:",
                          //   //   value: widget.currentItem.variants?[1].variant2OptionName
                          //   //            ??
                          //   //       "N/A",
                          //   // ),
                          //    Text(
                          //                 "${widget.currentItem.variants?[1].variant2OptionName}"??"N/A",
                          //                 style: TextStyle(
                          //                     fontWeight: FontWeight.bold),
                          //               ),
                          // ],
                          //                if (widget.currentItem.variants != null &&
                          //     widget.currentItem.variants?[2].variant3OptionName !=
                          //         null) ...[
                          //   const SizedBox(height: 4),
                          //   // EnquiryKeyValue(
                          //   //   key1:
                          //   //       "${widget.currentItem.variants?[2].variant3OptionId}:",
                          //   //   value: widget.currentItem.variants?[2].variant3OptionName
                          //   //            ??
                          //   //       "N/A",
                          //   // ),
                          //    Text(
                          //                 "${widget.currentItem.variants?[2].variant3OptionName}"??"N/A",
                          //                 style: TextStyle(
                          //                     fontWeight: FontWeight.bold),
                          //               ),
                          // ],

                          // if (!isBuyer() && widget.mode == 2)
                          // Column(
                          //   children: [
                          //     const SizedBox(height: 4),
                          //     EnquiryKeyValue(
                          //       key1: "Option 1:",
                          //       value: "" ?? "N/A",
                          //     ),
                          //     const SizedBox(height: 4),
                          //     EnquiryKeyValue(
                          //       key1: "Option 2:",
                          //       value: "" ?? "N/A",
                          //     ),
                          //     const SizedBox(height: 4),

                          //   ],
                          // ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    Stack(
                      children: [
                        Column(
                          children: [
                            //level 2
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                children: [
                                  // if (showEnquiryClosesOn())
                                  Expanded(
                                    flex: 1,
                                    child: Padding(
                                      padding: const EdgeInsets.all(2),
                                      child: Container(
                                        height: 48,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          border: Border.all(
                                            width: 1.0,
                                            color: Colors.black,
                                          ),
                                        ),
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(2),
                                            child: AutoSizeText(
                                              "Request Due Date: ${TimeUtils().formatBaiDate(DateFormat('yyyy-MM-dd HH:mm:ss').format(widget.currentItem.deliveryDate ?? DateTime.now()))}",
                                              textAlign: TextAlign.center,
                                              style: const TextStyle(
                                                color: Colors.red,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),

                                  // if (showEnquiryClosesOn()) const SizedBox(width: 4),

                                  if (widget.isNotify == false)
                                    Expanded(
                                      flex: 1,
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Container(
                                          height: 48,
                                          decoration: BoxDecoration(
                                            color: isOpened
                                                ? AppColors.red
                                                : AppColors.green,
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: TextButton(
                                            style: const ButtonStyle(
                                                // Change text color
                                                ),
                                            onPressed: () {
                                              setState(() {
                                                isOpened = !isOpened;
                                              });
                                            },
                                            child: Text(
                                              isOpened
                                                  ? "Close"
                                                  : "Open Attachments",
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            // copied from stocks
                            if ((widget.isNotify == false) ? isOpened : true)
                              BaiOfflineAttachments(item: widget.currentItem),
                            // last row
                            SizedBox(
                                height: kIsWeb
                                    ? 4
                                    : Platform.isIOS
                                        ? 4
                                        : 16),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        // Positioned(
        //   bottom: 0,
        //   child: BiddingInfoWidgetEditAssignApprove(
        //     content: widget.currentItem,
        //     changeStatus: () async {
        //       try {
        //         var api = NetworkController();
        //         var res = await api.changeStatus(widget.currentItem.prchOrdrId.toString());
        //         if (res.status == 200) {
        //           alert(res.statusDescription);
        //           // context.read<OfflineMainBloc>().add(
        //           //       InitializeOfflineStocks(
        //           //         offlineStocks: BaiProductsRes(),
        //           //         queryString: widget.query,
        //           //         code: widget.categoryType,
        //           //       ),
        //           //     );
        //         } else {
        //           alert(res.statusDescription);
        //         }
        //       } catch (e) {
        //         alert("Failed to change status, please try again later!");
        //       }
        //     },
        //     reload: () async {
        //       // context.read<OfflineMainBloc>().add(
        //       //       InitializeOfflineStocks(
        //       //         offlineStocks: BaiProductsRes(),
        //       //         queryString: widget.query,
        //       //         code: widget.categoryType,
        //       //       ),
        //       //     );
        //     },
        //   ),
        // )

        if (!widget.isEdited)
          Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: ((getRoleLevel() == 1) && isBuyer())
                ? BiddingInfoWidgetEditAssignApprove(
                    content: widget.currentItem,
                    changeStatus: () {
                      context
                          .read<NotificationDataCubit>()
                          .loadNotificationData(
                              widget.currentItem.prchOrdrId.toString());
                    },
                    reload: () {
                      context
                          .read<NotificationDataCubit>()
                          .loadNotificationData(
                              widget.currentItem.prchOrdrId.toString());
                    },
                  )
                : BiddingInfoWidgetHistoryOffersMore(
                    content: widget.currentItem,
                    reload: () {
                      context
                          .read<NotificationDataCubit>()
                          .loadNotificationData(
                              widget.currentItem.prchOrdrId.toString());
                    },
                    minimize: () {
                      setState(() {
                        isOpened = false;
                      });
                    },
                  ),
          )
      ]),
    );
  }

  Color getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll("#", "");
    if (hexColor.length == 6) {
      hexColor = "FF$hexColor"; // add opacity if not included
    }
    return Color(int.parse(hexColor, radix: 16));
  }
}
