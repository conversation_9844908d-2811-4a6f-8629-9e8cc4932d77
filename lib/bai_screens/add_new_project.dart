import 'dart:ui';

import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/core/bai_widgets/add_project_text_form_field.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:connectone/bai_blocs/purchases_bloc/purchases_cubit.dart';
import 'package:connectone/bai_models/add_project_req.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:location/location.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../bai_models/area_of_business_res.dart';
import '../core/bai_widgets/help_info.dart';
import '../core/network/network_controller.dart';

/// This widget is used to add a new project.
class AddNewProjectScreen extends StatefulWidget {
  /// Constructor for [AddNewProjectScreen].
  const AddNewProjectScreen({Key? key}) : super(key: key);

  @override

  /// Creates the state for [AddNewProjectScreen].
  State<AddNewProjectScreen> createState() => _AddNewProjectScreenState();
}

class _AddNewProjectScreenState extends State<AddNewProjectScreen> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _projectNameController = TextEditingController();
  final TextEditingController _projectAddressController =
      TextEditingController();
  final TextEditingController _addressLine2Controller = TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();
  final TextEditingController _pincodeController = TextEditingController();
  final TextEditingController _latitudeController = TextEditingController();
  final TextEditingController _longitudeController = TextEditingController();
  final TextEditingController _siteAccessController = TextEditingController();

  List<AreaOfBusiness>? areas = [];

  String? selectedOrgName;
  int? selectedOrgId = -1;

  String? selectedSiteAccess;
  String? selectedArea;
  String? selectedAreaId = "0";
  MapboxMap? mapboxMap;
  final Location _location = Location();

  void _onMapCreated(MapboxMap mapboxMap1) {
    mapboxMap = mapboxMap1;
  }

  /// Gets the current location of the device.
  ///
  /// Requests the location service to be enabled if it is not already.
  /// Requests the location permission if it is not already granted.
  ///
  /// Returns the current location if the service and permission are enabled,
  /// otherwise returns null.
  Future<LocationData?> _getCurrentLocation() async {
    // Check if location service is enabled
    var serviceEnabled = await _location.serviceEnabled();
    if (!serviceEnabled) {
      // Request location service to be enabled
      serviceEnabled = await _location.requestService();
      if (!serviceEnabled) {
        // Location service is not enabled
        safePrint('Location service not enabled');
        return null;
      }
    }

    // Check if location permission is granted
    final permissionStatus = await _location.hasPermission();

    if (permissionStatus != PermissionStatus.granted) {
      // Request location permission if it is not already granted
      final requestStatus = await _location.requestPermission();
      if (requestStatus != PermissionStatus.granted) {
        // Location permission is not granted
        safePrint('Location permission denied');
        return null;
      }
    }

    // Get the current location
    return await _location.getLocation();
  }

  /// Centers the map on the given location.
  ///
  /// This method takes a latitude and longitude and moves the map to that position.
  /// If the map is not initialized or the coordinates are null, this method does
  /// nothing.
  ///
  /// [latitude] The latitude of the location to center the map on.
  /// [longitude] The longitude of the location to center the map on.
  Future<void> _centerMapOnLocation(double? latitude, double? longitude) async {
    if (mapboxMap == null || latitude == null || longitude == null) return;

    try {
      // Set the latitude and longitude text controllers to the given values
      _latitudeController.text = latitude.toString();
      _longitudeController.text = longitude.toString();

      // Create a new camera options object with the given coordinates and a zoom
      // level of 14.0
      final center = Point(coordinates: Position(longitude, latitude));
      final cameraOptions = CameraOptions(center: center, zoom: 14.0);

      // Set the camera options on the map
      await mapboxMap!.setCamera(cameraOptions);

      // Print a message to the console with the coordinates of the new center
      // location
      safePrint('Camera moved to Latitude: $latitude, Longitude: $longitude');
    } catch (e) {
      // Print a message to the console with the error message
      safePrint('Error moving camera: ${e.toString()}');
    }
  }

  /// Gets the center coordinates of the map.
  ///
  /// This method gets the current center coordinates of the map and updates the
  /// latitude and longitude text controllers with the new values.
  ///
  /// If the map is not initialized or there is an error getting the center
  /// coordinates, this method does nothing.
  Future<void> _getCenterCoordinates() async {
    if (mapboxMap == null) return;

    try {
      // Get the current camera state
      final cameraState = await mapboxMap!.getCameraState();

      // Extract the coordinates from the camera state
      final longitude = cameraState.center.coordinates.lng;
      final latitude = cameraState.center.coordinates.lat;

      // Update the latitude and longitude text controllers with the new values
      _latitudeController.text = latitude.toString();
      _longitudeController.text = longitude.toString();

      // Print a message to the console with the new coordinates
      safePrint('Center Latitude: $latitude, Longitude: $longitude');
    } catch (e) {
      // Print a message to the console with the error message
      safePrint('Error getting center coordinates: ${e.toString()}');
    }
  }

  /// Move the camera to a new location.
  ///
  /// This method takes the text input from the latitude and longitude text
  /// controllers and attempts to parse them as doubles. If the parsing is
  /// successful, it creates a new camera position and animates the camera to
  /// the new position.
  ///
  /// If the parsing fails, this method shows an error message to the user.
  Future<void> _moveToNewLocation() async {
    // Get the current text input from the latitude and longitude text controllers
    final String latitudeText = _latitudeController.text;
    final String longitudeText = _longitudeController.text;

    // Try to parse the text input as doubles
    final double? latitude = double.tryParse(latitudeText);
    final double? longitude = double.tryParse(longitudeText);

    if (latitude != null && longitude != null) {
      // Create a new camera position
      final CameraOptions cameraOptions = CameraOptions(
        center: Point(coordinates: Position(longitude, latitude)),
        zoom: 14.0,
      );

      // Animate camera to the new position
      await mapboxMap?.flyTo(
        cameraOptions,
        MapAnimationOptions(
          duration: 1000, // Duration in milliseconds for the animation
        ),
      );

      // Print a message to the console with the new coordinates
      safePrint('Camera moved to Latitude: $latitude, Longitude: $longitude');
    } else {
      // Show error message if input is not valid
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter valid latitude and longitude values.'),
        ),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<PurchasesCubit>().getOrgIds();
    getAreas();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Site Creation!\n\nHere's how to add a new site:\n\n1. Fill in the site details like name and access type\n\n2. Enter the complete address information\n\n3. Use the map to pinpoint the exact location - drag to move\n\n4. Fine-tune latitude and longitude if needed\n\n5. Select the business area for proper categorization\n\nAll fields are important for accurate site registration. Use the map marker to ensure precise location.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        //vishnu-commented

        title: const Text('Add New Site'),
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<PurchasesCubit, PurchasesState>(
          listener: (context, state) {
            if (state is NewProjectAdded) {
              alert("New Site Added Successfully!");
              Navigator.pop(context);
            }
          },
          builder: (context, state) {
            (state is NewProjectLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            if (state is NewProjectLoaded) {
              return SizedBox(
                height: MediaQuery.of(context).size.height - 72,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      children: [
                        const SizedBox(height: 16),
                        AddProjectTextFormField(
                          labelText: 'Site Name',
                          controller: _projectNameController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Site Name is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'Site Access',
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.black,
                                width: 1,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.black,
                                width: 1,
                              ),
                            ),
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.black,
                                width: 1,
                              ),
                            ),
                            labelStyle: TextStyle(color: Colors.black),
                            isDense: true,
                          ),
                          items: BaiCart.siteAccessTypeList?.roadAccessTypes
                              ?.map((site) => DropdownMenuItem<String>(
                                    value: site.value,
                                    child: Text("${site.value}"),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedSiteAccess = value;
                            });
                          },
                          value: selectedSiteAccess,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Site Access is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        AddProjectTextFormField(
                          labelText: 'Address Line 1',
                          controller: _projectAddressController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Address Line 1 is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        AddProjectTextFormField(
                          labelText: 'Address Line 2',
                          controller: _addressLine2Controller,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Address Line 2 is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'District',
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.black,
                                width: 1,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.black,
                                width: 1,
                              ),
                            ),
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.black,
                                width: 1,
                              ),
                            ),
                            labelStyle: TextStyle(color: Colors.black),
                            isDense: true,
                          ),
                          items: areas
                              ?.map((area) => DropdownMenuItem<String>(
                                    value: area.id?.toString(),
                                    child: Text("${area.name}"),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedArea = value;
                              selectedAreaId = areas
                                      ?.firstWhere((area) =>
                                          area.id?.toString() == value)
                                      .id
                                      .toString() ??
                                  "0";
                            });
                          },
                          value: selectedArea,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'District is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        AddProjectTextFormField(
                          labelText: 'Area',
                          controller: _cityController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Area is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        AddProjectTextFormField(
                          labelText: 'State',
                          controller: _stateController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'State is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        AddProjectTextFormField(
                          labelText: 'Pincode',
                          controller: _pincodeController,
                          keyboardType: TextInputType.number,
                          maxLength: 6,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Pincode is required';
                            }
                            return null;
                          },
                        ),
                        Stack(
                          children: [
                            Container(
                              color: Colors.grey.shade200,
                              height: 200,
                              child: MapWidget(
                                key: const ValueKey("MapBoxWidget"),
                                onTapListener:
                                    (MapContentGestureContext context) async {
                                  safePrint(
                                      'Screen Coordinate: x=${context.touchPosition.x}, y=${context.touchPosition.y}');
                                  try {
                                    final longitude =
                                        context.point.coordinates.lng;
                                    final latitude =
                                        context.point.coordinates.lat;
                                    safePrint(
                                        'Latitude: $latitude, Longitude: $longitude');
                                  } catch (e) {
                                    safePrint(
                                        "Error accessing coordinates: ${e.toString()}");
                                  }
                                },
                                onScrollListener: (coordinate) {
                                  _getCenterCoordinates();
                                },
                                onMapCreated: (onMapCreated) async {
                                  _onMapCreated(onMapCreated);
                                  onMapCreated.gestures.updateSettings(
                                    GesturesSettings(
                                      scrollEnabled: true,
                                      pinchToZoomEnabled: true,
                                      quickZoomEnabled: true,
                                      scrollMode:
                                          ScrollMode.HORIZONTAL_AND_VERTICAL,
                                    ),
                                  );

                                  try {
                                    final currentLocation =
                                        await _getCurrentLocation();
                                    if (currentLocation != null) {
                                      _centerMapOnLocation(
                                          currentLocation.latitude,
                                          currentLocation.longitude);
                                    }
                                  } catch (e) {
                                    safePrint(
                                        'Error getting current location: ${e.toString()}');
                                  }
                                },
                                // ignore: prefer_collection_literals
                                gestureRecognizers: Set()
                                  ..add(Factory<PanGestureRecognizer>(
                                      () => PanGestureRecognizer()))
                                  ..add(Factory<ScaleGestureRecognizer>(
                                      () => ScaleGestureRecognizer()))
                                  ..add(Factory<TapGestureRecognizer>(
                                      () => TapGestureRecognizer()))
                                  ..add(Factory<OneSequenceGestureRecognizer>(
                                      () => EagerGestureRecognizer())),
                                cameraOptions: CameraOptions(
                                  center: Point(
                                    coordinates: Position(76.2144, 10.5276),
                                  ),
                                  zoom: 12.0,
                                ),
                              ),
                            ),
                            Positioned(
                              top: 88,
                              left: 140,
                              child: Image.asset(
                                "assets/images/pin_icon.png",
                                width: 32.0,
                                height: 32.0,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        AddProjectTextFormField(
                          labelText: 'Latitude',
                          controller: _latitudeController,
                          keyboardType: TextInputType.number,
                          onChanged: (value) async {
                            await _moveToNewLocation();
                          },
                        ),
                        const SizedBox(height: 16),
                        AddProjectTextFormField(
                          labelText: 'Longitude',
                          controller: _longitudeController,
                          keyboardType: TextInputType.number,
                          onChanged: (value) async {
                            await _moveToNewLocation();
                          },
                        ),
                        const SizedBox(height: 32),
                        SizedBox(
                          height: 52,
                          child: ElevatedButton(
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                _formKey.currentState!.save();
                                // Handle form submission
                                safePrint('Org Id: $selectedOrgName');
                                safePrint(
                                    'Project Name: ${_projectNameController.text}');
                                safePrint(
                                    'Site Access: ${_siteAccessController.text}');
                                safePrint(
                                    'Address Line 1: ${_projectAddressController.text}');
                                safePrint(
                                    'Address Line 2: ${_addressLine2Controller.text}');
                                safePrint('City: ${_cityController.text}');
                                safePrint('State: ${_stateController.text}');
                                safePrint(
                                    'Country: ${_countryController.text}');
                                safePrint(
                                    'Pincode: ${_pincodeController.text}');
                                safePrint(
                                    'Latitude: ${_latitudeController.text}');
                                safePrint(
                                    'Longitude: ${_longitudeController.text}');

                                AddProjectReq newProject = AddProjectReq(
                                  projectName:
                                      _projectNameController.text.trim(),
                                  siteAccess: selectedSiteAccess,
                                  customerOrgsId: selectedOrgId,
                                  createdBy: null,
                                  customerName: null,
                                  address: Address(
                                    addressLine1:
                                        _projectAddressController.text.trim(),
                                    addressLine2:
                                        _addressLine2Controller.text.trim(),
                                    city: _cityController.text.trim(),
                                    state: _stateController.text.trim(),
                                    // country: _countryController.text,
                                    country: "India",
                                    pincode: _pincodeController.text.trim(),
                                    latitude: double.tryParse(
                                        _latitudeController.text.trim()),
                                    longitude: double.tryParse(
                                        _longitudeController.text.trim()),
                                  ),
                                  areaOfBusinessId:
                                      int.tryParse(selectedAreaId ?? "0"),
                                );

                                context
                                    .read<PurchasesCubit>()
                                    .addNewProject(newProject);
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors
                                  .primaryColor, // Button background color
                              foregroundColor:
                                  Colors.white, // Button text color
                              padding:
                                  const EdgeInsets.symmetric(vertical: 16.0),
                              textStyle: const TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            child: const Text('NEXT'),
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              );
            } else {
              return const SizedBox.shrink();
            }
          },
        ),
      ),
    );
  }

  void getAreas() async {
    try {
      var networkController = NetworkController();
      var res = await networkController.getareaOfBusiness();
      var areas1 = res.areas;
      setState(() {
        areas = areas1;
      });
    } catch (e) {
      safePrint(e);
    }
  }
}
