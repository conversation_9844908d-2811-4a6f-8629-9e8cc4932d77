import 'package:connectone/bai_models/bai_products_res.dart' as bpr;
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/bai_screens/seller_offers_page.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/quote_summary_dialog.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/options_menu_utility.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/status_list_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../bai_blocs/cubit/status_dropdown_cubit.dart';
import '../core/bai_widgets/mr_summary_dialog.dart';

class EnquiryKeyValue extends StatelessWidget {
  const EnquiryKeyValue({
    Key? key,
    required this.key1,
    required this.value,
    this.color,
    this.bold = false,
  }) : super(key: key);

  final String key1;
  final String value;
  final Color? color;
  final bool bold;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: color ?? Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 2,
              child: Text(
                key1,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontWeight: bold ? FontWeight.bold : FontWeight.normal,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              flex: 3,
              child: Text(
                value,
                style: TextStyle(
                  color: key1 == "Buyer Company:" || key1 == "Buyer Phone:"
                      ? AppColors.primaryColor
                      : null,
                  fontSize: 16,
                  fontWeight: bold ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class BiddingInfoWidgetEditAssignApprove extends StatefulWidget {
  const BiddingInfoWidgetEditAssignApprove({
    Key? key,
    required this.content,
    required this.changeStatus,
    required this.reload,
    this.fromMrList,
  }) : super(key: key);

  final bpr.Content content;
  final Function changeStatus;
  final Function reload;
  final bool? fromMrList;

  @override
  State<BiddingInfoWidgetEditAssignApprove> createState() =>
      _BiddingInfoWidgetEditAssignApproveState();
}

class _BiddingInfoWidgetEditAssignApproveState
    extends State<BiddingInfoWidgetEditAssignApprove> {
  bool isEditAllowed() {
    // var designation = getDesignation().toLowerCase();
    var roleCode = getRoleLevel();
    return roleCode == 1 || roleCode == 5 || roleCode == 10;
  }

  bool isPostAllowed() {
    var roleCode = getRoleLevel();
    // var designation = getDesignation().toLowerCase();
    return roleCode == 1 || roleCode == 5 || roleCode == 10;
  }

  bool isLoading = false;

  List<Datum>? data;

  @override
  void initState() {
    super.initState();
    // context.read<StatusDropdownCubit>().loadStatuses(
    //       '${widget.content.statusCd}',
    //       widget.content.orderGroupId?.toInt().toString() ?? "",
    //     );
  }

  @override

  /// Builds the UI for the BiddingInfoWidgetEditAssignApprove.
  ///
  /// This widget displays a row of interactive items:
  /// - History dialog
  /// - Invite vendors dialog
  /// - Options menu
  ///
  /// [context] is the BuildContext for the widget.
  Widget build(BuildContext context) {
    return AppLoader(
      child: BlocConsumer<StatusDropdownCubit, StatusDropdownState>(
        listener: (context, state) {
          if (state is StatusDropdownLoaded) {
            setState(() {
              data = state.statusRes?.data;
            });
          }
        },
        builder: (context, state) {
          return Container(
            margin: const EdgeInsets.all(0),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12.0),
                bottomRight: Radius.circular(12.0),
              ),
              color: Colors.green.shade800,
            ),
            padding: const EdgeInsets.all(12.0),
            child: SizedBox(
              height: kIsWeb ? 100 : MediaQuery.of(context).size.width / 4.5,
              width: MediaQuery.of(context).size.width - 24,
              child: Row(
                children: [
                  // History button
                  // Expanded(
                  //   child: GestureDetector(
                  //     onTap: () {
                  //       showDialog(
                  //         context: context,
                  //         builder: (context) {
                  //           return HistoryDialog(
                  //             prchOrdrId: widget.content.prchOrdrId.toString(),
                  //           );
                  //         },
                  //       );
                  //     },
                  //     child: Container(
                  //       decoration: BoxDecoration(
                  //         color: Colors.white,
                  //         borderRadius: BorderRadius.circular(4),
                  //       ),
                  //       child: Column(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           Icon(
                  //             Icons.history,
                  //             color: AppColors.primaryColor,
                  //           ),
                  //           const SizedBox(height: 4),
                  //           const Text(
                  //             'HISTORY',
                  //             style: TextStyle(
                  //               fontSize: 12,
                  //               fontWeight: FontWeight.bold,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // const SizedBox(width: 8),
                  // Invite vendors button
                  Expanded(
                    child: GestureDetector(
                      onTapDown: (details) async {
                        if (widget.content.statusCd == "REJD") {
                          alert("You can't invite vendors for rejected order.");
                          return;
                        }
                        if (isPostAllowed()) {
                          var date = await showDialog(
                              context: context,
                              builder: (context) {
                                return MRSummaryDialog(
                                  prchOrdrSplitId:
                                      widget.content.prchOrdrSplitId.toString(),
                                  categoryId: widget.content.cappCategoriesId
                                      .toString(),
                                  orderGroupId:
                                      widget.content.orderGroupId.toString(),
                                );
                              });

                          if (date[0] == null) {
                            return;
                          }
                          showDialog(
                            context: context,
                            builder: (context) {
                              return QuoteSummaryDialog(
                                prchOrdrId:
                                    widget.content.orderGroupId?.toInt() ?? 0,
                                content: widget.content,
                                date: date[0],
                                categoryId: date[1]?.toString(),
                                steelMr: date[3] ?? false,
                              );
                            },
                          );
                        } else {
                          alert(
                              "You don't have the required permissions to post.");
                        }
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.arrow_circle_right_outlined,
                              color: AppColors.primaryColor,
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'INVITE VENDORS',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: GestureDetector(
                      onTapDown: (details) async {
                        print(
                            'data.splitId--widget.content.statusCd ${widget.content.statusCd}');

                        // var designation = getDesignation();
                        var roleCode = getRoleLevel();
                        //site edited
                        // var isSiteInCharge = roleCode == 15;
                        // if (isSiteInCharge) {
                        //   alert(
                        //       "You don't have required permission to view this page.");
                        //   return;
                        // }
                        if (widget.content.statusCd == "REJD") {
                          alert("This is a rejected order.");
                          return;
                        }
                        var date = await showDialog(
                            barrierColor: Colors.transparent,
                            context: context,
                            builder: (context) {
                              return MRSummaryDialog(
                                //lllll
                                prchOrdrSplitId:
                                    widget.content.prchOrdrId.toString(),
                                categoryId:
                                    widget.content.cappCategoriesId.toString(),
                                orderGroupId:
                                    widget.content.orderGroupId.toString(),
                              );
                            });

                        if (date[0] == null) {
                          return;
                        }

                        // Get to the quotes page
                        if (isBuyer()) {
                          Get.to(BuyerOffersPage(
                            item: widget.content,
                            showSummary: false,
                            deliveryDate: date[0],
                            categoryId: date[1]?.toString(),
                            splitName: date[2]?.toString(),
                            steelMr: date[3] ?? false,
                            splitStatus: date[4]?.toString(),
                            // prchOrdrId: date[4]?.toString(),
                          ))?.then((val) {
                            // widget.reload();
                          });
                        } else {
                          Get.to(SellerOffersPage(
                            item: widget.content,
                            showSummary: false,
                            deliveryDate: date[0],
                            categoryId: date[1]?.toString(),
                            splitName: date[2]?.toString(),
                            steelMr: date[3] ?? false,
                            // splitStatus: date[4]?.toString(),
                            // prchOrdrId: date[4]?.toString(),
                          ))?.then((val) {
                            // widget.reload();
                          });
                        }
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.arrow_circle_right_outlined,
                              color: AppColors.primaryColor,
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'QUOTES',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  // Options menu button
                  Expanded(
                    child: GestureDetector(
                      onTapDown: (TapDownDetails details) {
                        OptionsMenuUtility optionsMenuUtility =
                            OptionsMenuUtility(
                          context: context,
                          content: widget.content,
                          position: details.globalPosition,
                          prchOrdrId: [widget.content.prchOrdrId?.toInt() ?? 0],
                          fromMrList: widget.fromMrList,
                        );
                        optionsMenuUtility.fetchStatusDropdown();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.more_vert,
                              color: AppColors.primaryColor,
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'OPTIONS',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

Future<void> makePhoneCall(String phoneNumber) async {
  final Uri launchUri = Uri(
    scheme: 'tel',
    path: phoneNumber.isNotEmpty ? phoneNumber : "",
  );
  if (await canLaunchUrl(launchUri)) {
    await launchUrl(launchUri);
  } else {
    throw 'Could not launch $phoneNumber';
  }
}
