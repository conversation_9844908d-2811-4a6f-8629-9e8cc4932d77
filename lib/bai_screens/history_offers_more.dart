import 'package:connectone/bai_models/bai_products_res.dart' as bpr;
import 'package:connectone/bai_screens/mr_grouping.dart';
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/bai_screens/seller_offers_page.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/options_menu_utility.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/status_list_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../bai_blocs/cubit/status_dropdown_cubit.dart';
import '../core/bai_widgets/mr_summary_dialog.dart';

class BiddingInfoWidgetHistoryOffersMore extends StatefulWidget {
  const BiddingInfoWidgetHistoryOffersMore({
    Key? key,
    required this.content,
    required this.reload,
    required this.minimize,
    this.fromMrList,
  }) : super(key: key);

  final bpr.Content content;
  final Function reload;
  final Function minimize;
  final bool? fromMrList;

  @override
  State<BiddingInfoWidgetHistoryOffersMore> createState() =>
      _BiddingInfoWidgetHistoryOffersMoreState();
}

class _BiddingInfoWidgetHistoryOffersMoreState
    extends State<BiddingInfoWidgetHistoryOffersMore> {
  List<Datum>? data;

  @override
  void initState() {
    super.initState();
    // context.read<StatusDropdownCubit>().loadStatuses(
    //       '${widget.content.statusCd}',
    //       widget.content.orderGroupId?.toInt().toString() ?? "",
    //     );
  }

  @override

  /// Builds the widget
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<StatusDropdownCubit, StatusDropdownState>(
      /// Called when the state of the cubit changes
      listener: (context, state) {
        if (state is StatusDropdownLoaded) {
          // Update the local data when the state is loaded
          setState(() {
            data = state.statusRes?.data;
          });
        }
      },

      /// Builds the widget tree
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.all(0),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12.0),
              bottomRight: Radius.circular(12.0),
            ),
            color: Colors.green.shade800,
          ),
          padding: const EdgeInsets.all(12.0),
          child: SizedBox(
            height: kIsWeb ? 100 : MediaQuery.of(context).size.width / 4.5,
            width: MediaQuery.of(context).size.width - 24,
            child: Row(
              children: [
                // Expanded(
                //   child: GestureDetector(
                //     onTap: () {
                //       if (widget.content.statusCd == "REJD") {
                //         alert("This is a rejected order.");
                //         return;
                //       }
                //       // Get to the summary page
                //       // Get.to(MrGrouping(
                //       //   itemName: widget.content.orderGroupId.toString(),
                //       //   itemId: widget.content.orderGroupId.toString(),
                //       // ));
                //       Get.to(MrGrouping(
                //         itemName: widget.content.orderGroupId.toString(),
                //         itemId: widget.content.orderGroupId.toString(),
                //       ));
                //     },
                //     child: Container(
                //       decoration: BoxDecoration(
                //         color: Colors.white,
                //         borderRadius: BorderRadius.circular(4),
                //       ),
                //       child: Column(
                //         mainAxisAlignment: MainAxisAlignment.center,
                //         children: [
                //           Icon(
                //             Icons.summarize_outlined,
                //             color: AppColors.primaryColor,
                //           ),
                //           const SizedBox(height: 4),
                //           const Text(
                //             'SHOW ALL MR',
                //             style: TextStyle(
                //               fontSize: 12,
                //               fontWeight: FontWeight.bold,
                //             ),
                //           ),
                //         ],
                //       ),
                //     ),
                //   ),
                // ),
                // const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTapDown: (details) async {
                      print(
                          'data.splitId--widget.content.statusCd ${widget.content.statusCd}');
                      // var designation = getDesignation();
                      var roleCode = getRoleLevel();
                      //site edited

                      // var isSiteInCharge = roleCode == 15;
                      // if (isSiteInCharge) {
                      //   alert(
                      //       "You don't have required permission to view this page.");
                      //   return;
                      // }
                      if (widget.content.statusCd == "REJD") {
                        alert("This is a rejected order.");
                        return;
                      }
                      // Hide the widget
                      // widget.minimize();

                      var date = await showDialog(
                          context: context,
                          builder: (context) {
                            return MRSummaryDialog(
                              prchOrdrSplitId:
                                  widget.content.prchOrdrSplitId.toString(),
                              categoryId:
                                  widget.content.cappCategoriesId.toString(),
                              orderGroupId:
                                  widget.content.orderGroupId.toString(),
                            );
                          });

                      if (date[0] == null) {
                        return;
                      }

                      // Get to the quotes page
                      if (isBuyer()) {
                        Get.to(BuyerOffersPage(
                          splitStatus: date[4]?.toString(),
                          item: widget.content,
                          showSummary: false,
                          deliveryDate: date[0],
                          categoryId: date[1]?.toString(),
                          splitName: date[2]?.toString(),
                          steelMr: date[3] ?? false,
                        ))?.then((val) {
                          // widget.reload();
                        });
                      } else {
                        Get.to(SellerOffersPage(
                          // splitStatus: date[4]?.toString(),
                          item: widget.content,
                          showSummary: false,
                          deliveryDate: date[0],
                          categoryId: date[1]?.toString(),
                          splitName: date[2]?.toString(),
                          steelMr: date[3] ?? false,
                        ))?.then((val) {
                          // widget.reload();
                        });
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.arrow_circle_right_outlined,
                            color: AppColors.primaryColor,
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'QUOTES',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTapDown: (TapDownDetails details) {
                      // Show options menu
                      OptionsMenuUtility optionsMenuUtility =
                          OptionsMenuUtility(
                        context: context,
                        content: widget.content,
                        position: details.globalPosition,
                        prchOrdrId: [widget.content.prchOrdrId?.toInt() ?? 0],
                        fromMrList: widget.fromMrList,
                      );
                      optionsMenuUtility.fetchStatusDropdown();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.more_vert,
                            color: AppColors.primaryColor,
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'OPTIONS',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  bool isEditAllowed() {
    var roleCode = getRoleLevel();
    // var designation = getDesignation().toLowerCase();
    return roleCode == 1 || roleCode == 5 || roleCode == 10;
  }
}
