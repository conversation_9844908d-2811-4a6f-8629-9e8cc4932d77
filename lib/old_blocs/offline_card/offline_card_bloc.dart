import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_database/firebase_database.dart';

import '../../core/network/network_controller.dart';
import '../../old_models/firebase_response.dart';
import '../../core/utils/constants.dart';
import '../../core/utils/safe_print.dart';
import '../../core/utils/tools.dart';

part 'offline_card_event.dart';
part 'offline_card_state.dart';

class OfflineSubBloc extends Bloc<OfflineSubEvent, OfflineSubState> {
  final NetworkController networkController = NetworkController();

  OfflineSubBloc() : super(OfflineCardInitial()) {
    on<ToggleCard>((event, emit) {
      if (state is OfflineCardLoaded) {
        final state = this.state as OfflineCardLoaded;
        var btnText = state.buttonText;
        if (btnText == "Open Attachments") {
          btnText = "Close";
        } else if (btnText == "Close") {
          btnText = "Open Attachments";
        }
        emit(
          OfflineCardLoaded(
            isOpen: !state.isOpen,
            groupValue: state.groupValue,
            buttonText: btnText,
            isTable: state.isTable,
            currentItem: state.currentItem,
            firebaseResponse: state.firebaseResponse,
          ),
        );
      }
    });
    on<InitializeOfflineCard>((event1, emit) async {
      FirebaseResponseOffline firebaseResponse = FirebaseResponseOffline(
        stockAuctionStatus: "",
        bidUpdateTimestamp: "2022-10-07 12:30:00",
        stockStatusDescription: "",
        buyNowPrice: 0,
        highestBidCustomerId: 0,
        auctionEndTs: "2022-10-07 12:30:00",
        highestBid: 0,
      );
      DatabaseReference dbReference = FirebaseDatabase.instance
          .ref('$firebaseBaseUrl/offline_auction')
          .child(event1.currentItem);
      dbReference.onValue.listen((DatabaseEvent event) {
        if (event.snapshot.exists) {
          var data = event.snapshot;
          firebaseResponse = FirebaseResponseOffline.fromSnapshot(data);
          log(event.snapshot.value.toString());
          try {
            add(
              InitializeFb(
                event1.buttonText,
                event1.currentItem,
                event1.groupValue,
                event1.isOpen,
                event1.isTable,
                firebaseResponse,
              ),
            );
          } catch (e) {
            safePrint(e);
          }
        }
      });
    });
    on<InitializeFb>((event, emit) {
      emit(
        OfflineCardLoaded(
          isOpen: event.isOpen,
          groupValue: event.groupValue,
          buttonText: event.buttonText,
          isTable: event.isTable,
          currentItem: event.currentItem,
          firebaseResponse: event.firebaseResponse,
        ),
      );
    });
    on<ToggleTable>((event, emit) {
      if (state is OfflineCardLoaded) {
        final state = this.state as OfflineCardLoaded;
        emit(
          OfflineCardLoaded(
            isOpen: state.isOpen,
            groupValue: state.groupValue,
            buttonText: state.buttonText,
            isTable: !state.isTable,
            currentItem: state.currentItem,
            firebaseResponse: state.firebaseResponse,
          ),
        );
      }
    });
    on<AddAsFavourite>((event, emit) async {
      try {
        await networkController.addAsFavourite(
            customerId: event.customerId, stockId: event.stockId);
        // var favouritesResponse = await networkController.getNewFavourites();
      } catch (e) {
        alert("Something went wrong.");
      }
    });
    on<SetButtonText>((event, emit) {});
    on<SetGroupValue>((event, emit) {
      if (state is OfflineCardLoaded) {
        final state = this.state as OfflineCardLoaded;
        emit(
          OfflineCardLoaded(
            isOpen: state.isOpen,
            groupValue: event.groupValue,
            buttonText: state.buttonText,
            isTable: !state.isTable,
            currentItem: state.currentItem,
            firebaseResponse: state.firebaseResponse,
          ),
        );
      }
    });
    on<Hide>((event, emit) {
      if (state is OfflineCardLoaded) {
        final state = this.state as OfflineCardLoaded;
        emit(
          OfflineCardLoaded(
            isOpen: state.isOpen,
            groupValue: state.groupValue,
            buttonText: state.buttonText,
            isTable: state.isTable,
            currentItem: state.currentItem,
            firebaseResponse: state.firebaseResponse,
            isHidden: !state.isHidden,
          ),
        );
      }
    });
    on<Autobid>((event, emit) async {
      try {
        if (state is OfflineCardLoaded) {
          final state = this.state as OfflineCardLoaded;
          final response = await networkController.autoBid(
            limit: event.limit,
            orderTypeCd: event.orderTypeCd,
            stockId: event.stockId,
            bidDeskNo: event.bidDeskNo,
            increment: event.increment,
            quantity: event.quantity,
          );
          if (response.status == 200) {
            alert("Autobid successful!");
          } else {
            alert(response.statusDescription);
          }
          add(
            InitializeOfflineCard(
              state.buttonText,
              state.currentItem.toString(),
              state.groupValue,
              state.isOpen,
              state.isTable,
            ),
          );
        }
      } catch (e) {
        alert(sSomethingWentWrong);
      }
    });
    on<MakeOffer>((event, emit) async {
      try {
        if (state is OfflineCardLoaded) {
          final state = this.state as OfflineCardLoaded;
          final response = await networkController.makeOffer(
            orderTypeCd: event.orderTypeCd,
            stockId: event.stockId,
            bidDeskNo: event.bidDeskNo,
            quantity: event.quantity,
            amount: event.amount,
          );
          if (response.status == 200) {
            alert("Offer made successfully!");
          } else {
            alert(response.statusDescription);
          }
          add(
            InitializeOfflineCard(
              state.buttonText,
              state.currentItem.toString(),
              state.groupValue,
              state.isOpen,
              state.isTable,
            ),
          );
        }
      } catch (e) {
        alert("You are the seller of this stock!");
      }
    });
    on<MakeOffline>((event, emit) async {
      try {
        if (state is OfflineCardLoaded) {
          final state = this.state as OfflineCardLoaded;
          final response = await networkController.makeOffline(
            orderTypeCd: event.orderTypeCd,
            stockId: event.stockId,
            quantity: event.quantity,
            amount: event.amount,
            vendorId: event.vendorId,
            bidDate: event.bidDate,
            auctionId: event.auctionId,
            roomId: event.roomId,
          );
          if (response.status == 200) {
            alert("Offline bid successful!");
          } else {
            alert(response.statusDescription);
          }
          add(
            InitializeOfflineCard(
              state.buttonText,
              state.currentItem.toString(),
              state.groupValue,
              state.isOpen,
              state.isTable,
            ),
          );
        }
      } catch (e) {
        alert("You are the seller of this stock!");
      }
    });
    on<BuyNow>((event, emit) async {
      try {
        if (state is OfflineCardLoaded) {
          final response = await networkController.buyNow(
            orderTypeCd: event.orderTypeCd,
            stockId: event.stockId,
            bidDeskNo: event.bidDeskNo,
            quantity: event.quantity,
            amount: event.amount,
          );
          if (response.status == 200) {
            alert("Bought successfully!");
          } else {
            alert(response.statusDescription);
          }
        }
      } catch (e) {
        alert(sSomethingWentWrong);
      }
    });
    on<ShowAutobid>((event, emit) async {
      if (state is OfflineCardLoaded) {
        final state = this.state as OfflineCardLoaded;
        emit(OfflineCardLoaded(
          isOpen: state.isOpen,
          groupValue: state.groupValue,
          buttonText: state.buttonText,
          isTable: state.isTable,
          currentItem: state.currentItem,
          firebaseResponse: state.firebaseResponse,
          showAutobid: true,
        ));
      }
    });
    on<ToggleMoreOptions>((event, emit) {
      if (state is OfflineCardLoaded) {
        final state = this.state as OfflineCardLoaded;
        // Future.delayed(const Duration(seconds: 3), () {
        //   emit(OfflineCardLoaded(
        //       isOpen: state.isOpen,
        //       groupValue: state.groupValue,
        //       buttonText: state.buttonText,
        //       isTable: state.isTable,
        //       currentItem: state.currentItem,
        //       firebaseResponse: state.firebaseResponse,
        //       isMoreOptionsOpen: event.isMoreOptionsOpen));
        // });
      }
    });
    on<CancelAutobid>((event, emit) async {
      if (state is OfflineCardLoaded) {
        final state = this.state as OfflineCardLoaded;
        var response =
            await NetworkController().cancelAutobid(stockId: event.stockId);
        if (response.status == 200) {
          alert("Autobid cancelled!");
        } else {
          alert(response.statusDescription);
        }
        emit(
          OfflineCardLoaded(
            isOpen: state.isOpen,
            groupValue: state.groupValue,
            buttonText: state.buttonText,
            isTable: state.isTable,
            currentItem: state.currentItem,
            firebaseResponse: state.firebaseResponse,
            showAutobid: true,
          ),
        );
      }
    });
  }
}
