import 'package:amazon_cognito_identity_dart_2/cognito.dart';
import 'package:connectone/old_blocs/forgot_password/forgot_password_state.dart';
import 'package:connectone/old_screens/login_screen.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../core/network/network_controller.dart';
import '../../core/utils/constants.dart';

class ForgotPasswordBloc extends Cubit<ForgotPasswordState> {
  ForgotPasswordBloc(initialState) : super(initialState);
  final _networkController = NetworkController();
  FirebaseAuth auth = FirebaseAuth.instance;
  String? vCognitoToken;

  willPopClick() {
    var newState = state.copyWith(
        confirmationCode: !state.confirmationCode,
        emailEditable: !state.emailEditable);
    emit(newState);
  }

  forgotPassword(String username) async {
    if (username.isEmpty) {
      alert(sEnterAValidEmail);
      return;
    }

    var newState = state.copyWith(isLoading: true);
    emit(newState);

    // For phone numbers, verify customer exists first
    if (!username.contains("@")) {
      try {
        var verifyResponse =
            await _networkController.verifyCustomerForForgotPassword(username);
        if (verifyResponse.status != 200) {
          var newState = state.copyWith(isLoading: false);
          emit(newState);
          // Show the error message from the response
          String errorMessage = verifyResponse.statusDescription;
          if (errorMessage.isEmpty) {
            errorMessage =
                "Customer not found. Please check your phone number and try again.";
          }
          alert(errorMessage);
          return;
        }
        // If verification successful, show success message and proceed with Cognito
        alert("OTP sent successfully");
        username = "+91$username";
      } catch (e) {
        var newState = state.copyWith(isLoading: false);
        emit(newState);
        alert("Failed to verify customer. Please try again.");
        return;
      }
    } else {
      username = username; // Keep email as is
    }

    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    final cognitoUser = CognitoUser(username, userPool);
    try {
      var data = await cognitoUser.forgotPassword();
      if (username.contains("@")) {
        alert("$sCodeIsSendTo $username");
      }
      safePrint(data);
      var newState = state.copyWith(
          isLoading: false, confirmationCode: true, emailEditable: false);
      emit(newState);
    } catch (e) {
      safePrint(e);
      var newState = state.copyWith(isLoading: false);
      emit(newState);
      if (e is CognitoClientException) {
        // Check for specific error types related to user not found
        if (e.message?.contains("UserNotFoundException") == true ||
            e.message?.contains("User does not exist") == true ||
            e.message?.contains("Username/client id combination not found") ==
                true) {
          alert(sPhoneNotRegistered);
        } else {
          alert(e.message ?? sSomethingWentWrong);
        }
      } else {
        // Check if the error string contains user not found indicators
        String errorString = e.toString().toLowerCase();
        if (errorString.contains("usernotfoundexception") ||
            errorString.contains("user does not exist") ||
            errorString.contains("user not found")) {
          alert(sPhoneNotRegistered);
        } else {
          alert(sSomethingWentWrong);
        }
      }
    }
  }

  Future<bool> confirmPassword(String username, String code, String newPassword,
      String confirmPassword) async {
    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    if (isPhoneNumber) {
      username = "+91$username";
    }
    safePrint("pppp $username $code $newPassword $confirmPassword");
    final cognitoUser = CognitoUser(username, userPool);
    try {
      if (code.isEmpty) {
        alert(sEnterAValidCode);
        return false;
      }
      if (newPassword.isEmpty || newPassword != confirmPassword) {
        alert("Please enter correct passwords!");
        return false;
      }
      var newState = state.copyWith(isLoading: true);
      emit(newState);
      var passwordConfirmed =
          await cognitoUser.confirmPassword(code, newPassword);

      var newState1 = state.copyWith(isLoading: false);
      emit(newState1);
      alert(sPasswordChangedSuccessfully);
      Get.off(() => const LoginScreen());
      return passwordConfirmed;
    } catch (e) {
      safePrint("qqqq $e");
      var newState = state.copyWith(isLoading: false);
      emit(newState);
      if (e is CognitoClientException) {
        alert(e.message);
      } else {
        alert(sPasswordChangeFailed);
      }
      return false;
    }
  }
}
