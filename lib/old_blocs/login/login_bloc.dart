import 'dart:developer';

import 'package:amazon_cognito_identity_dart_2/cognito.dart';
import 'package:connectone/bai_models/razorpay_order_req.dart';
import 'package:connectone/old_blocs/login/login_state.dart';
import 'package:connectone/old_screens/login_screen.dart';
import 'package:connectone/core/utils/storage_utils.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../core/network/network_controller.dart';
import '../../core/utils/constants.dart';

class LoginBloc extends Cubit<LoginState> {
  LoginBloc(initialState) : super(initialState);
  final _networkController = NetworkController();
  FirebaseAuth auth = FirebaseAuth.instance;
  String? vCognitoToken;

  initLogin({required String user, required String pass}) {
    var email = user;
    var password = pass;
    cognitoLogin(email, password);
  }

  forgotPassword(String username) async {
    if (username.isEmpty || !username.isEmail) {
      alert(sEnterAValidEmail);
      return;
    }
    var newState = state.copyWith(isLoading: true);
    emit(newState);
    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    final cognitoUser = CognitoUser(username, userPool);
    try {
      await cognitoUser.forgotPassword();
      alert("$sCodeIsSendTo $username");
    } catch (e) {
      var newState = state.copyWith(isLoading: false);
      emit(newState);
      if (e is CognitoClientException) {
        // Check for specific error types related to user not found
        if (e.message?.contains("UserNotFoundException") == true ||
            e.message?.contains("User does not exist") == true ||
            e.message?.contains("Username/client id combination not found") ==
                true) {
          alert(sEmailNotRegistered);
        } else {
          alert(e.message ?? sSomethingWentWrong);
        }
      } else {
        // Check if the error string contains user not found indicators
        String errorString = e.toString().toLowerCase();
        if (errorString.contains("usernotfoundexception") ||
            errorString.contains("user does not exist") ||
            errorString.contains("user not found")) {
          alert(sEmailNotRegistered);
        } else {
          alert(sSomethingWentWrong);
        }
      }
    }
  }

  Future<bool> confirmPassword(String username, String code, String newPassword,
      String confirmPassword) async {
    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    final cognitoUser = CognitoUser(username, userPool);
    try {
      if (code.isEmpty) {
        alert(sEnterAValidCode);
        return false;
      }
      if (newPassword.isEmpty || newPassword != confirmPassword) {
        alert(sEnterAValidCode);
        return false;
      }
      var passwordConfirmed =
          await cognitoUser.confirmPassword(code, newPassword);
      alert(sPasswordChangedSuccessfully);
      Get.off(() => const LoginScreen());
      return passwordConfirmed;
    } catch (e) {
      if (e is CognitoClientException) {
        alert(e.message);
      } else {
        alert(sPasswordChangeFailed);
      }
      return false;
    }
  }

  cognitoLogin(String username, String password) async {
    if (username.isEmpty) {
      alert(sEnterAValidUsername);
      return;
    }
    if (!username.contains("@")) {
      username = "+91$username";
    }
    if (password.isEmpty) {
      alert(sEnterAValidPassword);
      return;
    }
    var nState = state.copyWith(isLoading: true);
    emit(nState);
    try {
      final userPool = CognitoUserPool(
        _networkController.organisationData?.userPoolId ?? "",
        _networkController.organisationData?.appClientWeb ?? "",
      );
      final cognitoUser = CognitoUser(username, userPool);
      final authDetails = AuthenticationDetails(
        username: username,
        password: password,
      );
      CognitoUserSession? session;
      List<CognitoUserAttribute>? attributes;
      try {
        session = await cognitoUser.authenticateUser(authDetails);
        attributes = await cognitoUser.getUserAttributes();
      } catch (e) {
        var nState = state.copyWith(isLoading: false);
        emit(nState);
        if (e is CognitoClientException) {
          alert(e.message);
        } else {
          alert("Login failed!");
        }
        log(e.toString());
      }
      vCognitoToken = session?.getIdToken().getJwtToken();
      if (vCognitoToken != null) {
        writeToStorage(cognitoToken, vCognitoToken!);
      }
      var role = attributes
          ?.firstWhere((element) => element.getName() == "custom:role_name")
          .getValue();
      if (role != null) {
        writeToStorage(customerRole, role);
      }
      var vCustomerId = attributes
          ?.firstWhere(
              (element) => element.getName() == "custom:c1_customer_id")
          .getValue();
      if (vCustomerId != null) {
        writeToStorage(customerId, vCustomerId);
      }
      _firebaseLogin();
    } catch (e) {
      var nState = state.copyWith(isLoading: false);
      emit(nState);
      log(e.toString());
    }
  }

  _firebaseLogin() async {
    try {
      var response = await _networkController
          .firebaseAuth(
        vCognitoToken,
        _networkController.organisationData?.userPoolId,
      )
          .catchError((onError) async {
        var nState = state.copyWith(isLoading: false);
        emit(nState);
        log(onError.toString());
      });
      if (response.data != null) {
        try {
          var user = await auth.signInWithCustomToken(response.data);
        } catch (e) {
          print(e.toString());
          return;
        }
        var user = await auth.signInWithCustomToken(response.data);
        if (user.user != null) {
          writeToStorage(loggedIn, "TRUE");
          var newState = state;
          final fcmToken = await FirebaseMessaging.instance.getToken();
          if (fcmToken != null) {
            _networkController.sendToken(fcmToken: fcmToken);
          }
          var profile = await _networkController.getProfile();
          var data = profile.data[0];
          saveName(data.firstName.toString());
          savePhone(data.phone.toString());
          saveEmail(data.email.toString());
          writeToStorage("username", data.firstName.toString());
          var res = await _networkController.getUserV2();
          var dataV2 = res.data?[0].vendor;
          saveVendorId(dataV2?.id.toString() ?? "");
          saveVendorName(dataV2?.name.toString() ?? "");
          saveDesignation(res.data?[0].designation.toString() ?? "");
          saveDesignationId(res.data?[0].userLevel.toString() ?? "");
          saveVendorPhone(dataV2?.primaryPhone.toString() ?? "");
          var subscriptionRequired = dataV2?.subscriptionRequired ?? false;
          var subscriptionAmountPending =
              dataV2?.subscriptionAmountPending ?? 0;
          if (subscriptionRequired && subscriptionAmountPending > 0) {
            var req = CreateRazorpayOrderReq(
              amount: subscriptionAmountPending.toInt(),
              event: "subscription_create",
            );
            var orderId = await _networkController.getRazorPayOrderId(req);
            emit(newState.copyWith(
              userV2: res,
              orderId: orderId.orderId,
              loginSuccess: true,
            ));
          } else {
            emit(newState.copyWith(userV2: res, loginSuccess: true));
          }
          emit(state.copyWith(loginSuccess: false));
        } else {
          alert(sLoginFailed);
        }
      } else {
        alert(sLoginFailed);
      }
      var nState = state.copyWith(isLoading: false);
      emit(nState);
    } catch (e) {
      var nState = state.copyWith(isLoading: false);
      emit(nState);
      alert(sLoginFailed);
    }
  }

  void getNewUserDetails() async {
    var res = await _networkController.getUserV2();
    var data = res.data?[0].vendor;
    var subscriptionRequired = data?.subscriptionRequired ?? false;
    var subscriptionAmountPending = data?.subscriptionAmountPending ?? 0;
    if (subscriptionRequired && subscriptionAmountPending > 0) {
      _getRazorPayOrderId(amount: subscriptionAmountPending.toInt());
    }
  }

  void _getRazorPayOrderId({required int amount}) async {
    var req = CreateRazorpayOrderReq(
      amount: amount,
      event: "subscription_create",
    );
    await _networkController.getRazorPayOrderId(req);
  }
}
