import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../old_blocs/help/help_bloc.dart';
import '../core/network/network_controller.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';

// ignore: must_be_immutable
class HelpScreen extends StatelessWidget {
  final String url = Get.arguments[0];
  final NetworkController networkController = NetworkController();

  HelpScreen({Key? key}) : super(key: key);

  String getUrl(String passedUrl) {
    if (passedUrl.contains("https")) {
      return passedUrl;
    } else {
      return "https://coc.freshdesk.com/support/solutions";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        title: Text(networkController.organisationData?.organizationName ?? ""),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.close),
            color: Colors.red,
            onPressed: () {
              Get.back();
            },
          )
        ],
      ),
      body: BlocProvider(
        create: (context) => HelpBloc()..add(const LoadHelp()),
        child: BlocBuilder<HelpBloc, HelpState>(
          builder: (context, state) {
            if (state is HelpInitial) {
              return Stack(
                children: [
                  WebViewWidget(
                    controller: WebViewController()
                      ..setJavaScriptMode(JavaScriptMode.unrestricted)
                      ..setNavigationDelegate(
                        NavigationDelegate(
                          onPageFinished: (string) {
                            context.read<HelpBloc>().add(const LoadedHelp());
                          },
                        ),
                      )
                      ..loadRequest(Uri.parse(getUrl(url))),
                  ),
                  if (state.isLoading) Center(child: progressIndicator),
                ],
              );
            } else {
              return const Center(
                child: Text("Error occurred!"),
              );
            }
          },
        ),
      ),
    );
  }
}

class HelpScreen1 extends StatelessWidget {
  final String fromWhere = Get.arguments[0];
  final String url = Get.arguments[1];
  final NetworkController networkController = NetworkController();

  HelpScreen1({Key? key}) : super(key: key);

  String getUrl(String urlPage) {
    return "https://www.connectoneclub.com/contactus";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        title: Text(networkController.organisationData?.organizationName ?? ""),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.close),
            color: Colors.red,
            onPressed: () {
              Get.back();
            },
          )
        ],
      ),
      body: BlocProvider(
        create: (context) => HelpBloc()..add(const LoadHelp()),
        child: BlocBuilder<HelpBloc, HelpState>(
          builder: (context, state) {
            if (state is HelpInitial) {
              return Stack(
                children: [
                  WebViewWidget(
                    controller: WebViewController()
                      ..setJavaScriptMode(JavaScriptMode.unrestricted)
                      ..setNavigationDelegate(
                        NavigationDelegate(
                          onPageFinished: (string) {
                            context.read<HelpBloc>().add(const LoadedHelp());
                          },
                        ),
                      )
                      ..loadRequest(Uri.parse(
                          fromWhere == "CONTACTUS" ? url : getUrl(fromWhere))),
                  ),
                  if (state.isLoading) Center(child: progressIndicator),
                ],
              );
            } else {
              return const Center(
                child: Text("Error occurred!"),
              );
            }
          },
        ),
      ),
    );
  }
}
