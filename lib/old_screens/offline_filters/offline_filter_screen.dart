import 'dart:collection';
import 'dart:ui';

import 'package:connectone/bai_models/bai_filter_res.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/filter_multi_select.dart';
import 'package:connectone/core/bai_widgets/help_info.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_screens/offline_filters/offline_filters_bloc.dart';
import 'package:connectone/old_screens/offline_filters/search_events.dart';
import 'package:connectone/old_screens/offline_filters/search_filters_bloc.dart';
import 'package:connectone/core/utils/calendar_utils.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../core/network/network_controller.dart';
import '../../core/utils/app_routes.dart';

// var savedQuery = Get.

class OfflineFilterScreen extends StatefulWidget {
  const OfflineFilterScreen({
    Key? key,
    required this.title,
    required this.userType,
    required this.categoryType,
    // required this.tileCode,
    required this.isAdmin,
  }) : super(key: key);

  final String title;
  final String userType;
  final String categoryType;
  // final String tileCode;
  final bool isAdmin;

  @override
  State<OfflineFilterScreen> createState() => _OfflineFilterScreenState();
}

Map<String, List<int>> parseQueryString(String query) {
  Map<String, List<int>> result = {};
  if (query.trim().isEmpty) {
    return result;
  }

  List<String> parameters = query.split('&');

  for (String parameter in parameters) {
    List<String> parts = parameter.split('=');
    if (parts.length == 2) {
      String key = parts[0].trim();
      String valueString = parts[1].trim();
      List<int> values = [];
      try {
        values =
            valueString.split(',').map((e) => int.parse(e.trim())).toList();
      } catch (e) {
        continue;
      }
      result[key] = values;
    }
  }
  return result;
}

class _OfflineFilterScreenState extends State<OfflineFilterScreen> {
  NetworkController networkController = NetworkController();

  var queryString = "";

  var category = "";

  var filterProducts = [];
  var filterLocation = [];
  var filterGrade = [];
  var filterProductsFull = [];
  var filterLocationFull = [];
  var filterGradeFull = [];
  var filterDeliveryDate = [];
  bool buyNowSlider = false;
  bool priceSlider = false;
  bool quantitySlider = false;
  SfRangeValues buyNowValues = const SfRangeValues(0.0, 0.0);
  SfRangeValues priceSliderValues = const SfRangeValues(0.0, 0.0);
  SfRangeValues quantityValues = const SfRangeValues(0.0, 0.0);
  String quantity = '';
  String priceRange = '';
  String deliveryDate = '';
  String buyBidPrice = '';
  bool quantityActive = false;
  bool priceActive = false;
  bool buyNowActive = false;
  bool calVisible0 = false;
  bool calVisible1 = false;

  var selectedSearchId = 0;
  var selectedSearchName = "";

  DateTime selectedDay = DateTime.now();

  final firstDay = DateTime(
      DateTime.now().year, DateTime.now().month - 12, DateTime.now().day);

  final lastDay = DateTime(
      DateTime.now().year, DateTime.now().month + 12, DateTime.now().day);

  PageController pageController = PageController();

  final ValueNotifier<DateTime> focusedDay = ValueNotifier(DateTime.now());

  final Set<DateTime> selectedDays = LinkedHashSet<DateTime>(
    equals: isSameDay,
    hashCode: getHashCode,
  );

  var sum = 0;

  DateTime? _rangeStart;
  DateTime? _rangeEnd;

  String year = DateTime.now().year.toString();
  String month = DateTime.now().month.toString();

  void showCal() {
    calVisible0 = true;
  }

  void hideCal() {
    calVisible0 = false;
  }

  List<String> categoryCodes = [];
  String? selectedCategory;

  void resetSliders() {
    quantityActive = false;
    priceActive = false;
    buyNowActive = false;
    quantitySlider = false;
    priceSlider = false;
    buyNowSlider = false;
  }

  void resetFilters() {
    filterProducts = [];
    filterLocation = [];
    filterGrade = [];
    filterDeliveryDate = [];
    selectedSearchName = "";
    buyNowValues = const SfRangeValues(0.0, 0.0);
    priceSliderValues = const SfRangeValues(0.0, 0.0);
    quantityValues = const SfRangeValues(0.0, 0.0);
    quantity = '';
    priceRange = '';
    deliveryDate = '';
    buyBidPrice = '';
  }

  bool get canClearSelection =>
      selectedDays.isNotEmpty || _rangeStart != null || _rangeEnd != null;

  void onDaySelected(DateTime selectedDay, DateTime focusedDay1) {
    setState(() {
      if (selectedDays.isNotEmpty) {
        selectedDays.clear();
        selectedDays.add(selectedDay);
      } else {
        selectedDays.add(selectedDay);
      }
      focusedDay.value = focusedDay1;
    });
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to the Filters Screen!\n\n"
                    "Here you can filter and view your material requests or purchase orders based on different criteria.\n\n"
                    "Key Features:\n"
                    "• Filter Dropdowns: Use multiple selection dropdowns to filter by location, product, status and more\n"
                    "• Summary Table: The table shows grouped data with columns for:\n"
                    "  - Site/Buyer: Location or buyer information\n"
                    "  - Category/Product: Type of material or product\n"
                    "  - Create/Delivery Date: Relevant dates\n"
                    "  - MRs: Count of material requests\n\n"
                    "• Click any row to view detailed requests for that specific combination\n"
                    "• Use 'Clear Filters' to reset all selections\n"
                    "• Total count is shown at the bottom with option to view all requests",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override
  void initState() {
    context.read<OfflineFilterCubit>().getFilters(
        isBuyer() ? getFilterB() : getFilterS(), widget.categoryType);
    context.read<SearchFilterBloc>().add(LoadFilters());
    // categoryCodes = DataMap.getValues(widget.tileCode) ?? [];
    // selectedCategory = categoryCodes.first;
    // context.read<OfflineFilterCubit>().changedFilters(
    //       getFilter(),
    //       widget.categoryType,
    //     );
    setQuery();
    createTutorial();
    super.initState();
  }

  setQuery() {
    queryString = isBuyer() ? getFilterB() : getFilterS();
  }

  var data = BaiFilterRes();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OfflineFilterCubit, OfflineFilterState>(
        builder: (context, state) {
      return Scaffold(
        appBar: AppBar(
          title: Text(widget.title),
          backgroundColor: AppColors.primaryColor,
          elevation: 0,
          actions: [
            InfoHelp(
              key: key1,
              url: "https://www.youtube.com/shorts/vFWjM-Mp0c4",
              onTap: () {
                showTutorial();
              },
            ),
          ],
        ),
        body: AppLoader(
          child: BlocConsumer<OfflineFilterCubit, OfflineFilterState>(
            listener: (context, state) {
              if (state is FilterLoaded) {
                var savedFilters = isBuyer() ? getFilterB() : getFilterS();
                var parsedFilters = parseQueryString(savedFilters);
                var filters = state.filterRes.filters;
                if (filters != null) {
                  for (var filter in filters) {
                    if (parsedFilters.containsKey(filter.filterName)) {
                      List<String> selectedValueNames = [];
                      List<int>? savedIds = parsedFilters[filter.filterName];
                      if (savedIds != null) {
                        for (var id in savedIds) {
                          for (var value in filter.values ?? []) {
                            if (value.id == id) {
                              selectedValueNames.add(value.name ?? "");
                              break;
                            }
                          }
                        }
                      }
                      context.read<OfflineFilterCubit>().updateSelectedValues(
                            filters.indexOf(filter),
                            selectedValueNames,
                          );
                    }
                  }
                }

                setState(() {
                  sum = 0;
                  var a = state.filterRes.summary?.length ?? 0;
                  for (var i = 0; i < a; i++) {
                    sum += state.filterRes.summary?[i].stockCount ?? 0;
                  }
                  data = state.filterRes;
                });
              }
            },
            builder: (context, state) {
              (state is FilterLoading)
                  ? context.loaderOverlay.show()
                  : context.loaderOverlay.hide();
              return SafeArea(
                child: Stack(
                  children: [
                    SingleChildScrollView(
                      physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 6),
                            Row(
                              children: [
                                BlocBuilder<SearchFilterBloc,
                                    SearchFilterState>(
                                  builder: (context, state) {
                                    if (state is Loading) {
                                      return SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width -
                                                24,
                                        child: const SizedBox(
                                          height: 40,
                                          child: Center(
                                            child: SizedBox(
                                              height: 16,
                                              width: 16,
                                              child: CircularProgressIndicator(
                                                  strokeWidth: 2),
                                            ),
                                          ),
                                        ),
                                      );
                                    } else if (state is SearchDataLoaded) {
                                      return const SizedBox.shrink();
                                    } else {
                                      return const SizedBox.shrink();
                                    }
                                  },
                                ),
                              ],
                            ),
                            BlocConsumer<OfflineFilterCubit,
                                OfflineFilterState>(
                              builder: (context, state) {
                                if (state is FilterLoaded) {
                                  return Column(
                                    children: [
                                      ListView.separated(
                                        itemCount:
                                            state.filterRes.filters?.length ??
                                                0,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemBuilder: (context, index) {
                                          var item =
                                              state.filterRes.filters?[index];
                                          var type = item?.type;
                                          var filterName =
                                              item?.filterName ?? "";

                                          if (type == "select") {
                                            return Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                filterName
                                                        .toLowerCase()
                                                        .contains("status")
                                                    ? FilterMultiSelectRequestsDropdown(
                                                        items: item!.values!
                                                            .map((e) =>
                                                                "${e.name?.toStatusName()} (${e.stockCount})")
                                                            .toList(),
                                                        onChanged:
                                                            (selectedValues) {
                                                          selectedValues =
                                                              selectedValues =
                                                                  selectedValues
                                                                      .map(
                                                                          (str) {
                                                            return str
                                                                .replaceAll(
                                                                    RegExp(
                                                                        r'\s*\(\d+\)'),
                                                                    '')
                                                                .toStatusCode();
                                                          }).toList();
                                                          List<String> selectedIds = item
                                                              .values!
                                                              .where((e) =>
                                                                  selectedValues
                                                                      .contains(e
                                                                          .name))
                                                              .map((e) => e.id
                                                                  .toString())
                                                              .toList();
                                                          List<String>
                                                              queryParams =
                                                              queryString
                                                                      .isNotEmpty
                                                                  ? queryString
                                                                      .split(
                                                                          '&')
                                                                  : [];
                                                          queryParams.removeWhere(
                                                              (param) =>
                                                                  param.split(
                                                                      '=')[0] ==
                                                                  item.filterName);
                                                          if (selectedValues
                                                              .isNotEmpty) {
                                                            var query =
                                                                "${item.filterName}=${selectedIds.join(",")}";
                                                            if (queryParams
                                                                .isEmpty) {
                                                              queryString =
                                                                  query;
                                                            } else {
                                                              queryString =
                                                                  "${queryParams.join('&')}&$query";
                                                            }
                                                          } else {
                                                            queryString =
                                                                queryParams
                                                                    .join('&');
                                                          }
                                                          isBuyer()
                                                              ? saveFilterB(
                                                                  queryString)
                                                              : saveFilterS(
                                                                  queryString);
                                                          context
                                                              .read<
                                                                  OfflineFilterCubit>()
                                                              .updateSelectedValues(
                                                                  index,
                                                                  selectedValues);
                                                          context
                                                              .read<
                                                                  OfflineFilterCubit>()
                                                              .changedFilters(
                                                                  queryString,
                                                                  widget
                                                                      .categoryType);
                                                        },
                                                        labelText:
                                                            item.filterDisplayName ??
                                                                '',
                                                        selectedValues: state
                                                                .selectedValues[
                                                            index],
                                                      )
                                                    : FilterMultiSelectDropdown(
                                                        items: item!.values!
                                                            .map((e) =>
                                                                "${e.name.toString()} (${e.stockCount})")
                                                            .toList(),
                                                        onChanged:
                                                            (selectedValues) {
                                                          selectedValues =
                                                              selectedValues
                                                                  .map((str) {
                                                            return str.replaceAll(
                                                                RegExp(
                                                                    r'\s*\(\d+\)'),
                                                                '');
                                                          }).toList();
                                                          List<String> selectedIds = item
                                                              .values!
                                                              .where((e) =>
                                                                  selectedValues
                                                                      .contains(e
                                                                          .name))
                                                              .map((e) => e.id
                                                                  .toString())
                                                              .toList();
                                                          List<String>
                                                              queryParams =
                                                              queryString
                                                                      .isNotEmpty
                                                                  ? queryString
                                                                      .split(
                                                                          '&')
                                                                  : [];
                                                          queryParams.removeWhere(
                                                              (param) =>
                                                                  param.split(
                                                                      '=')[0] ==
                                                                  item.filterName);
                                                          if (selectedValues
                                                              .isNotEmpty) {
                                                            var query =
                                                                "${item.filterName}=${selectedIds.join(",")}";
                                                            if (queryParams
                                                                .isEmpty) {
                                                              queryString =
                                                                  query;
                                                            } else {
                                                              queryString =
                                                                  "${queryParams.join('&')}&$query";
                                                            }
                                                          } else {
                                                            queryString =
                                                                queryParams
                                                                    .join('&');
                                                          }
                                                          isBuyer()
                                                              ? saveFilterB(
                                                                  queryString)
                                                              : saveFilterS(
                                                                  queryString);
                                                          context
                                                              .read<
                                                                  OfflineFilterCubit>()
                                                              .updateSelectedValues(
                                                                  index,
                                                                  selectedValues);
                                                          context
                                                              .read<
                                                                  OfflineFilterCubit>()
                                                              .changedFilters(
                                                                  queryString,
                                                                  widget
                                                                      .categoryType);
                                                        },
                                                        labelText:
                                                            item.filterDisplayName ??
                                                                '',
                                                        selectedValues: state
                                                                .selectedValues[
                                                            index],
                                                      ),
                                              ],
                                            );
                                          } else if (type == "range") {
                                          } else {
                                            return const SizedBox.shrink();
                                          }
                                          return null;
                                        },
                                        shrinkWrap: true,
                                        separatorBuilder:
                                            (BuildContext context, int index) {
                                          return const SizedBox(height: 8);
                                        },
                                      ),
                                      // if (categoryCodes.length > 1)
                                      // const SizedBox(height: 16),

                                      // SizedBox(
                                      //   width: double.infinity,
                                      //   child: DropdownButtonFormField<String>(
                                      //     style: const TextStyle(
                                      //       fontWeight: FontWeight.bold,
                                      //       color: Colors.black,
                                      //       fontFamily: "poppins",
                                      //     ),
                                      //     decoration: InputDecoration(
                                      //       focusedBorder: border,
                                      //       enabledBorder: border,
                                      //       border: border,
                                      //       contentPadding: const EdgeInsets.symmetric(
                                      //         horizontal: 16,
                                      //         vertical: 14,
                                      //       ),
                                      //       labelText: "Request Type",
                                      //       labelStyle: const TextStyle(
                                      //         color: Colors.black,
                                      //         fontWeight: FontWeight.bold,
                                      //         fontSize: 14,
                                      //       ),
                                      //       isDense: true,
                                      //     ),
                                      //     value: selectedCategory,
                                      //     onChanged: (String? newValue) {
                                      //       setState(() {
                                      //         selectedCategory = newValue;
                                      //         context.read<OfflineFilterCubit>().getFilters(selectedCategory ?? "");
                                      //       });
                                      //     },
                                      //     items: categoryCodes.map<DropdownMenuItem<String>>((String value) {
                                      //       return DropdownMenuItem<String>(
                                      //         value: value,
                                      //         child: Text(getFullForm(value)),
                                      //       );
                                      //     }).toList(),
                                      //   ),
                                      // ),
                                    ],
                                  );
                                } else {
                                  return const SizedBox.shrink();
                                }
                              },
                              listener: (context, state) {},
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                // Expanded(
                                //   child: SizedBox(
                                //     height: 40,
                                //     child: TextButton(
                                //       style: TextButton.styleFrom(
                                //         backgroundColor: AppColors.red,
                                //         shape: RoundedRectangleBorder(
                                //           borderRadius: BorderRadius.circular(12),
                                //         ),
                                //       ),
                                //       onPressed: () {
                                //         if (selectedSearchName.isNotEmpty) {
                                //           showDialog(
                                //               context: context,
                                //               builder: (BuildContext context) {
                                //                 return DeleteSearchDialog(
                                //                   title: "Delete Search",
                                //                   id: selectedSearchId,
                                //                   name: selectedSearchName,
                                //                 );
                                //               }).then((value) async {
                                //             await Future.delayed(const Duration(seconds: 3));
                                //             context.read<SearchFilterBloc>().add(LoadFilters());
                                //             setState(() {
                                //               resetFilters();
                                //               resetSliders();
                                //             });
                                //             context.read<OfflineFilterBloc>().add(GetFilters(category));
                                //           });
                                //         } else {
                                //           alert("Please select a saved search before proceeding.");
                                //         }
                                //       },
                                //       child: const Text(
                                //         "Delete Search",
                                //         style: TextStyle(color: Colors.white),
                                //         overflow: TextOverflow.ellipsis,
                                //       ),
                                //     ),
                                //   ),
                                // ),
                                // const SizedBox(width: 8),
                                // Expanded(
                                //   child: SizedBox(
                                //     height: 40,
                                //     child: TextButton(
                                //       style: TextButton.styleFrom(
                                //         backgroundColor: AppColors.green,
                                //         shape: RoundedRectangleBorder(
                                //           borderRadius: BorderRadius.circular(12),
                                //         ),
                                //       ),
                                //       onPressed: () {
                                //         var product = filterProducts.join(",");
                                //         var location = filterLocation.join(",");

                                //         showDialog(
                                //             context: context,
                                //             builder: (BuildContext context) {
                                //               return SaveSearchDialog(
                                //                 title: "Save Search",
                                //                 product: product,
                                //                 location: location,
                                //                 grade: filterGrade.join(","),
                                //                 quantityStart: quantityValues.start.toStringAsFixed(1),
                                //                 priceStart: priceSliderValues.start.toStringAsFixed(1),
                                //                 buyBidPriceStart: buyNowValues.start.toStringAsFixed(1),
                                //                 quantityEnd: quantityValues.end.toStringAsFixed(1),
                                //                 priceEnd: priceSliderValues.end.toStringAsFixed(1),
                                //                 buyBidPriceEnd: buyNowValues.end.toStringAsFixed(1),
                                //               );
                                //             }).then((value) async {
                                //           await Future.delayed(const Duration(seconds: 3));
                                //           BlocProvider.of<SearchFilterBloc>(context).add(LoadFilters());
                                //         });
                                //       },
                                //       child: const Text(
                                //         "Save Search",
                                //         style: TextStyle(color: Colors.white),
                                //         overflow: TextOverflow.ellipsis,
                                //       ),
                                //     ),
                                //   ),
                                // ),
                                // const SizedBox(width: 8),
                                Expanded(
                                  child: SizedBox(
                                    height: 50,
                                    child: TextButton(
                                      style: TextButton.styleFrom(
                                        elevation: 1,
                                        backgroundColor: Colors.orange,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(24),
                                        ),
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          resetFilters();
                                          resetSliders();
                                          queryString = "";
                                          hideCal();
                                        });
                                        isBuyer()
                                            ? saveFilterB("")
                                            : saveFilterS("");
                                        context
                                            .read<OfflineFilterCubit>()
                                            .clearFilters();
                                        context
                                            .read<OfflineFilterCubit>()
                                            .getFilters(
                                                "", widget.categoryType);
                                      },
                                      child: const Text(
                                        "Clear Filters",
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.primaryColor,
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(6),
                                  topLeft: Radius.circular(6),
                                  bottomLeft: Radius.circular(0),
                                  bottomRight: Radius.circular(0),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          flex: 6,
                                          child: Text(
                                            isBuyer() ? "Site" : "Buyer",
                                            style: const TextStyle(
                                                color: AppColors.white),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 6,
                                          child: Text(
                                            isBuyer() ? "Category" : "Product",
                                            textAlign: TextAlign.center,
                                            style: const TextStyle(
                                                color: AppColors.white),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 6,
                                          child: Text(
                                            // isBuyer()
                                            // ?
                                            "Create Date",
                                            // : "Delivery Date",
                                            textAlign: TextAlign.center,
                                            style: const TextStyle(
                                                color: AppColors.white),
                                          ),
                                        ),
                                        const Expanded(
                                          flex: 2,
                                          child: Text(
                                            "MRs",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: AppColors.white),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            (data.summary != null && data.summary!.isNotEmpty)
                                ? Container(
                                    decoration: const BoxDecoration(
                                      borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(6),
                                        bottomRight: Radius.circular(6),
                                      ),
                                    ),
                                    clipBehavior: Clip.hardEdge,
                                    child: ListView(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(
                                              parent: BouncingScrollPhysics()),
                                      padding: const EdgeInsets.only(bottom: 0),
                                      children: data.summary
                                              ?.map<Widget>((e) =>
                                                  GestureDetector(
                                                    onTap: () {
                                                      String projectName = "";
                                                      String vendorName = "";
                                                      if (isBuyer()) {
                                                        projectName = e.fields
                                                                ?.projectId
                                                                ?.toString() ??
                                                            '';
                                                      } else {
                                                        vendorName = e.fields
                                                                ?.vendorId
                                                                ?.toString() ??
                                                            '';
                                                      }
                                                      var siteName = isBuyer()
                                                          ? projectName
                                                          : vendorName;
                                                      var qs = "";
                                                      qs = isBuyer()
                                                          ? "projectId=$siteName&cappCategoriesId=${e.fields?.cappCategoriesId}&createdAt=${formatDateTime(e.fields?.createdAt)}"
                                                          : "vendorId=$siteName&mvtItemId=${e.fields?.mvtItemId}&deliveryDate=${formatDateTime(e.fields?.deliveryDate)}";
                                                      Get.toNamed(
                                                          AppRoutes
                                                              .offlineScreen,
                                                          arguments: {
                                                            'title':
                                                                widget.title,
                                                            'userType':
                                                                widget.userType,
                                                            'isAdmin':
                                                                widget.isAdmin,
                                                            'query': qs,
                                                            'categoryType':
                                                                selectedCategory,
                                                          });
                                                      // Get.to(OfflineScreen(
                                                      //   query: qs,
                                                      //   title: widget.title,
                                                      //   userType:
                                                      //       widget.userType,
                                                      //   categoryType:
                                                      //       selectedCategory ??
                                                      //           "",
                                                      //   isAdmin: widget.isAdmin,
                                                      // ));
                                                    },
                                                    child: Container(
                                                      color:
                                                          Colors.grey.shade100,
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(8.0),
                                                        child: Row(
                                                          children: [
                                                            Expanded(
                                                              flex: 6,
                                                              child: Text(
                                                                (isBuyer()
                                                                        ? e.fields
                                                                            ?.projectIdName
                                                                        : e.fields
                                                                            ?.vendorIdName) ??
                                                                    "N/A",
                                                                style:
                                                                    const TextStyle(
                                                                  color: Colors
                                                                      .black,
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ),
                                                            Expanded(
                                                              flex: 7,
                                                              child: Text(
                                                                isBuyer()
                                                                    ? e.fields
                                                                            ?.cappCategoriesIdName ??
                                                                        "N/A"
                                                                    : e.fields
                                                                            ?.mvtItemIdName ??
                                                                        "N/A",
                                                                textAlign:
                                                                    TextAlign
                                                                        .start,
                                                                style:
                                                                    const TextStyle(
                                                                  color: Colors
                                                                      .black,
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ),
                                                            Expanded(
                                                              flex: 6,
                                                              child: Text(
                                                                // isBuyer()?
                                                                '${e.fields?.createdAt?.toCreatedOn()}',
                                                                // ? e.fields?.createdAt?.toDeliveryOn() ??
                                                                //     "N/A"
                                                                // : e.fields?.deliveryDate?.toDeliveryOn() ??
                                                                //     "N/A",
                                                                textAlign:
                                                                    TextAlign
                                                                        .start,
                                                                style:
                                                                    const TextStyle(
                                                                  color: Colors
                                                                      .black,
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ),
                                                            Expanded(
                                                              flex: 1,
                                                              child: Text(
                                                                e.stockCount
                                                                    .toString(),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                                style:
                                                                    const TextStyle(
                                                                  color: Colors
                                                                      .black,
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ))
                                              .toList() ??
                                          [],
                                    ),
                                  )
                                : const SizedBox(
                                    height: 72,
                                    child: Center(child: Text("No Data Found")),
                                  ),
                            const SizedBox(height: 80),
                          ],
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomLeft,
                      child: Container(
                        color: AppColors.primaryColor,
                        width: double.maxFinite,
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Row(
                            children: [
                              const SizedBox(width: 8),
                              Text(
                                isPO()
                                    ? "Purchase\nOrders : $sum"
                                    : "Material\nRequests : $sum",
                                style: const TextStyle(
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              TextButton(
                                style: TextButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8)),
                                ),
                                onPressed: () {
                                  Get.toNamed(AppRoutes.offlineScreen,
                                      arguments: {
                                        'title': widget.title,
                                        'userType': widget.userType,
                                        'isAdmin': widget.isAdmin,
                                        'query': queryString,
                                        'categoryType': selectedCategory,
                                      });
                                  // Get.to(OfflineScreen(
                                  //   query: queryString,
                                  //   title: widget.title,
                                  //   userType: widget.userType,
                                  //   categoryType: selectedCategory ?? "",
                                  //   isAdmin: widget.isAdmin,
                                  // ));
                                },
                                child: Text(
                                  isPO()
                                      ? "View Purchase Orders"
                                      : "View Material Requests",
                                  style: TextStyle(
                                    color: AppColors.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );
    });
  }

  bool isPO() {
    if (widget.categoryType == 'PORD' ||
        widget.categoryType == 'OPOR' ||
        widget.categoryType == 'CPOR' ||
        widget.categoryType == 'EPOR') {
      return true;
    } else {
      return false;
    }
  }

  CalendarStyle _calendarStyle() => CalendarStyle(
        isTodayHighlighted: false,
        selectedDecoration: BoxDecoration(
            shape: BoxShape.circle, color: AppColors.primaryColor),
        selectedTextStyle: const TextStyle(color: Colors.white),
        todayTextStyle: const TextStyle(color: Colors.white),
        holidayDecoration: BoxDecoration(
            shape: BoxShape.circle, color: AppColors.primaryColor),
        holidayTextStyle: const TextStyle(color: Colors.white),
        todayDecoration:
            const BoxDecoration(color: Colors.grey, shape: BoxShape.circle),
      );

  // bool _holidayPredicate(DateTime day, List<Summary> calender) {
  //   String formattedDay = DateFormat('yyyy-MM-dd').format(day);
  //   for (var cal in calender) {
  //     if (cal.deliveryDate != null) {
  //       String formattedDeliveryDate = DateFormat('yyyy-MM-dd').format(cal.deliveryDate!);
  //       if (formattedDeliveryDate == formattedDay) {
  //         return true;
  //       }
  //     }
  //   }
  //   return false;
  // }
}

String formatDateTime(DateTime? dateTime) {
  if (dateTime == null) return "";
  final DateFormat formatter = DateFormat('yyyy-MM-dd');
  return formatter.format(dateTime);
}
