import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../old_blocs/sell_product/sell_product_bloc.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import '../core/utils/tools.dart';

class SellProductsWebView extends StatelessWidget {
  final url = Get.arguments[0];

  SellProductsWebView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          SellProductBloc()..add(const InitializeSellProductScreen(true)),
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: AppColors.primaryColor,
          title: const Text("Sell Your Products"),
        ),
        body: BlocBuilder<SellProductBloc, SellProductState>(
          builder: (context, state) {
            return Stack(
              children: [
                WebViewWidget(
                  controller: WebViewController()
                    ..setJavaScriptMode(JavaScriptMode.unrestricted)
                    ..setNavigationDelegate(
                      NavigationDelegate(
                        onPageFinished: (string) {
                          context
                              .read<SellProductBloc>()
                              .add(const LoadingCompleted(false));
                        },
                      ),
                    )
                    ..loadRequest(
                      Uri.parse(Uri.encodeFull(
                          "$url?auth_token=${getAuthToken() ?? ""}")),
                      headers: {
                        "Authorization": "Bearer ${getAuthToken() ?? ""}"
                      },
                    ),
                ),
                if (state is SellProductLoading && state.isLoading)
                  Center(child: progressIndicator),
              ],
            );
          },
        ),
      ),
    );
  }
}
