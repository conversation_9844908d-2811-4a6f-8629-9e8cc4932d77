import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/calculator_res.dart';
import 'package:connectone/core/network/network_controller.dart';

import '../../../bai_models/calculator_req.dart';

part 'calculator_state.dart';

class CalculatorCubit extends Cubit<CalculatorState> {
  CalculatorCubit() : super(CalculatorInitial());

  var api = NetworkController();

  void calculate(
    List<int>? offerIds,
    num transportation,
    num discount,
  ) async {
    var req = CalculatorReq(
      offersId: offerIds,
      transportationCharges: transportation,
      discount: discount,
    );
    // emit(CalculatorLoading());
    try {
      var res = await api.calculatePrice(req);
      emit(CalculationDone(res));
    } catch (e) {
      // ignore
    }
  }
}
