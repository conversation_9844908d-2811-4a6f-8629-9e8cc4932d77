import 'dart:async';

import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_blocs/quotes_only/cubit/quotes_only_cubit.dart';
import 'package:connectone/bai_blocs/cubit/post_seller_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/offers_req.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_models/seller_offers_res.dart';
import 'package:connectone/bai_models/summary_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/filter_popup.dart';
import 'package:connectone/core/bai_widgets/seller_card.dart';
import 'package:connectone/core/bai_widgets/buyer_quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/negotiation_card.dart';
import 'package:connectone/core/bai_widgets/negotiate_dialog.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/others.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_blocs/offline_stocks/offline_stocks_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bai_models/offers_filter_res.dart';

class SellerQuotesDialog extends StatefulWidget {
  const SellerQuotesDialog({
    required this.prchOrdrId,
    required this.itemDetails,
    this.index = 0,
    required this.statusCd,
    Key? key,
  }) : super(key: key);

  final int prchOrdrId;
  final String? statusCd;
  final Detail? itemDetails;
  final int? index;

  @override
  State<SellerQuotesDialog> createState() => _SellerQuotesDialogState();
}

class _SellerQuotesDialogState extends State<SellerQuotesDialog> {
  bool isLoading = false;
  int _selectedIndex = 0;
  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  List<Offer> offersList = [];
  late BaiProductsRes baiProductsRes;

  var req = ViewOfferReq(
    variant1OptionGroupName: [],
    variant1OptionNames: [],
    variant2OptionGroupName: [],
    variant2OptionNames: [],
    variant3OptionGroupName: [],
    variant3OptionNames: [],
  );

  List<SummaryResponse> summaryList = [];
  OffersFilterRes filter = OffersFilterRes(
    variant1OptionGroupName: [],
    variant1OptionNames: [],
    variant2OptionGroupName: [],
    variant2OptionNames: [],
    variant3OptionGroupName: [],
    variant3OptionNames: [],
  );
  var quantity = "0";
  List<SellerOffer> sellerOffers = [];
  List<TextEditingController> offerControllers = [];
  List<TextEditingController> finalofferControllers = [];
  List<TextEditingController> commentControllers = [];
  List<TextEditingController> quantityControllers = [];

  List<List<String>> images = [];
  List<List<String>> files = [];
  List<List<String>> audios = [];
  List<List<Uint8List>> imageBytes = [];
  List<List<Uint8List>> fileBytes = [];

  List<OffersReq> offersReq = [];
  List<AlreadySubmittedQuote> alreadySubmittedQuotes = [];

  TextEditingController gstController = TextEditingController();
  double? currentGstValue;

  SellerOffers? fullResponse;

  String getQuantity(SellerOffer item) {
    if (!isSteelReinforcement()) {
      return widget.itemDetails?.quantity?.toString() ?? "0.0";
    }

    for (int i = 0; i < 3; i++) {
      final groupName = [
        item.variant1OptionGroupName,
        item.variant2OptionGroupName,
        item.variant3OptionGroupName,
      ][i];

      if ((groupName ?? "").toLowerCase().contains("size")) {
        return [
          item.variant1Quantity?.toString() ?? "0.0",
          item.variant2Quantity?.toString() ?? "0.0",
          item.variant3Quantity?.toString() ?? "0.0",
        ][i];
      }
    }

    return "0.0";
  }

  String getUnit(SellerOffer item) {
    if (!isSteelReinforcement()) {
      return baiProductsRes.content?[0].optionName ?? "N/A";
    }

    for (int i = 0; i < 3; i++) {
      final groupName = [
        item.variant1OptionGroupName,
        item.variant2OptionGroupName,
        item.variant3OptionGroupName,
      ][i];

      if ((groupName ?? "").toLowerCase().contains("size")) {
        return [
              item.variant1Unit,
              item.variant2Unit,
              item.variant3Unit,
            ][i] ??
            "N/A";
      }
    }

    return "N/A";
  }

  bool showQuantity() {
    return !(widget.itemDetails?.mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement");
  }

  bool isSteelReinforcement() {
    return (widget.itemDetails?.mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement");
  }

  @override
  void initState() {
    super.initState();
    // We'll set the default tab after data is loaded
    _selectedIndex = widget.index ?? 0; // Temporarily set to Quotes tab

    // Add listener to GST controller to update calculations when GST changes
    gstController.addListener(() {
      final newGstValue = double.tryParse(gstController.text);
      if (newGstValue != currentGstValue) {
        setState(() {
          currentGstValue = newGstValue;
        });
      }
    });

    _loadData();
  }

  void _loadData() {
    context.read<QuotesOnlyCubit>().loadQuotesSeller(
          widget.prchOrdrId.toString(),
          req,
          0,
        );
  }

  void onSubmit() {
    offersReq.clear();
    for (int i = 0; i < offerControllers.length; i++) {
      var paths = [...images[i], ...audios[i], ...files[i]];
      var bytes = [...imageBytes[i], ...fileBytes[i]];
      var item = sellerOffers[i];
      var content = baiProductsRes.content?[0];
      final offerText = cleanRupeeString(finalofferControllers[i].text);
      var isSubmitted = getAlreadySubmittedQuote(
            item,
            alreadySubmittedQuotes,
          ) !=
          null;
      if (offerText.isNotEmpty && !isSubmitted) {
        offersReq.add(OffersReq(
          mvtItemId: content?.mvtItemId,
          mvtItemName: content?.mvtItemName,
          optionGroupId: content?.optionGroupId,
          optionGroupName: content?.optionGroupName,
          optionName: content?.optionName,
          variant1OptionGroupId: item.variant1OptionGroupId,
          variant1OptionGroupName: item.variant1OptionGroupName,
          variant1OptionName: item.variant1OptionName,
          variant2OptionGroupId: item.variant2OptionGroupId,
          variant2OptionGroupName: item.variant2OptionGroupName,
          variant2OptionName: item.variant2OptionName,
          variant3OptionGroupId: item.variant3OptionGroupId,
          variant3OptionGroupName: item.variant3OptionGroupName,
          variant3OptionName: item.variant3OptionName,
          variant1OptionId: item.variant1OptionId,
          variant2OptionId: item.variant2OptionId,
          variant3OptionId: item.variant3OptionId,
          optionId: content?.optionId,
          offerPrice: double.tryParse(offerText),
          customerId: double.parse(getCustomerId()),
          vendorId: content?.vendorId,
          prchOrdrId: content?.prchOrdrId,
          statusCd: content?.statusCd,
          remarks: commentControllers[i].text,
          quantity: double.tryParse(quantityControllers[i].text) ?? 0,
          perUnitPrice: (double.tryParse(offerText) ?? 1) /
              (double.tryParse(quantityControllers[i].text) ?? 1),
          paths: paths,
          bytes: bytes,
        ));
      }
    }
    // Call PostSellerCubit before submitting offers

    context.read<QuotesOnlyCubit>().postSellerOffers(
          offersReq,
          baiProductsRes.content![0].mvtItemId.toString(),
          gstController.text,
          fullResponse?.isEnableGst ?? false,
          "QUOT",
        );
    context.read<PostSellerCubit>().postSeller();
  }

  bool hideProductName() {
    return false;
  }

  @override
  Widget build(BuildContext context) {
    var keyboardVisible = MediaQuery.of(context).viewInsets.bottom != 0;
    return BlocListener<OffersCubit, OffersState>(
      listener: (context, offersState) {
        print('-------state-------offersState ${offersState}');
        // ... (listener code remains the same)
        if (offersState is RefreshSellerPage ||
            offersState is NotInterestedSuccess ||
            offersState is BuyerOffersSuccess ||
            offersState is BuyerOffersNegotiated ||
            offersState is NegotiationAccepted ||
            offersState is AcceptNegotiateFailed ||
            offersState is DiscardSuccess ||
            offersState is BuyerOffersLoaded ||
            offersState is BuyerOffersFailed) {
          _loadData();
        }

        if (offersState is NotifyBuyerSuccess) {
          alert(offersState.message);
          _loadData();
        }
        if (offersState is RefreshSellerQuotesPage) {
          _loadData();
        }

        if (offersState is NotifyBuyerFailed) {
          alert("Failed to notify buyer: ${offersState.message}");
          _loadData();
        }

        if (offersState is BuyerOffersLoading) {
          setState(() {
            isLoading = true;
          });
        }
        if (offersState is PostSellerSuccess) {
          context.read<OfflineMainBloc>().add(RefreshOfflineStocks());
        }
      },
      child: BlocListener<PostSellerCubit, PostSellerState>(
        listener: (context, postSellerState) {
          print('-------state-------postSellerState ${postSellerState}');
          if (postSellerState is PostSellerSuccess) {
            // alert("Post seller action completed successfully.");
            // Timer(const Duration(seconds: 2), () {
            // Navigator.of(context).pop();
            //   Navigator.of(context).pop();
            // });
          }
        },
        child: BlocConsumer<QuotesOnlyCubit, QuotesOnlyState>(
          listener: (context, state) {
            // ... (listener code remains the same)
            if (state is SellerQuotesLoading) {
              setState(() {
                isLoading = true;
              });
            }
            if (state is SellerQuotesSuccess) {
              alert("Quotes submitted successfully.");
              context.read<OffersCubit>().refreshSellerPage();
              context.read<OffersCubit>().refreshSellerQuotesPage();
              // Do not pop here, just refresh
            }
            if (state is SellerQuotesLoaded) {
              setState(() {
                isLoading = false;
                baiProductsRes = state.itemsContent;
                filter = state.filter;
                quantity = widget.itemDetails!.quantity.toString();
                if (state.offers.productGst != null) {
                  gstController.text = state.offers.productGst.toString();
                  currentGstValue = state.offers.productGst?.toDouble();
                }
                fullResponse = state.offers;
              });
              sellerOffers = state.offers.variants ?? [];
              // Initialize controllers for each offer
              offerControllers = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => TextEditingController(),
              );
              finalofferControllers = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => TextEditingController(),
              );
              commentControllers = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => TextEditingController(),
              );
              quantityControllers = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => TextEditingController()
                  ..text =
                      getQuantity(state.offers.variants![index]).toString(),
              );
              images = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => [],
              );
              imageBytes = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => [],
              );
              fileBytes = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => [],
              );
              files = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => [],
              );
              audios = List.generate(
                state.offers.variants?.length ?? 0,
                (index) => [],
              );
              alreadySubmittedQuotes = state.offers.alreadySubmittedQuote
                      ?.where((element) =>
                          element.negotiationHistory?.isNotEmpty == true)
                      .toList() ??
                  [];
              final negotiations = sellerOffers
                  .where((e) =>
                      getAlreadySubmittedQuote(e, alreadySubmittedQuotes) !=
                      null)
                  .toList();
              setState(() {
                _selectedIndex = negotiations.isNotEmpty ? 1 : 0;
              });
            }
          },
          builder: (context, state) {
            if (state is SellerQuotesLoading) {
              return const Center(
                child: SizedBox(
                  height: 40,
                  width: 40,
                  child: CircularProgressIndicator(),
                ),
              );
            }
            if (state is SellerQuotesLoaded) {
              var mrStatus = state.offers.mrStatus;
              var enableSubmit = ["QUOT", "VASS", "NEGO"].contains(mrStatus);
              var allowNego = ["NEGO", "QUOT"].contains(mrStatus);

              return Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                insetPadding: const EdgeInsets.all(8),
                child: Container(
                  padding: const EdgeInsets.all(
                      8), // Overall padding for dialog content
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    // Main layout column
                    mainAxisSize: MainAxisSize
                        .min, // Important for Dialogs to not overflow if content is small
                    // but with Expanded, it will try to fill available space.
                    children: [
                      // Non-scrollable top content
                      _buildHeader().withCloseButton(() {
                        Navigator.pop(context);
                      }),
                      const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 8,
                          right: 8,
                          bottom: 6,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: hideProductName()
                                      ? const SizedBox()
                                      : Text(
                                          '${baiProductsRes.content?[0].mvtItemName}',
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                          maxLines: 3,
                                        ),
                                ),
                                const SizedBox(width: 16),
                                hideProductName()
                                    ? const SizedBox()
                                    : IconButton(
                                        constraints: const BoxConstraints(),
                                        padding: EdgeInsets.zero,
                                        icon: const Icon(
                                            Icons.filter_alt_outlined),
                                        onPressed: () {
                                          showDialog(
                                              context: context,
                                              builder: (context) {
                                                return FilterPopup(
                                                  data: filter,
                                                  viewOfferReq: req,
                                                  onApply: (filterReq) {
                                                    req = filterReq;
                                                    context
                                                        .read<QuotesOnlyCubit>()
                                                        .loadQuotesSeller(
                                                            baiProductsRes
                                                                    .content?[0]
                                                                    .prchOrdrId
                                                                    .toString() ??
                                                                "",
                                                            filterReq,
                                                            baiProductsRes
                                                                    .content?[0]
                                                                    .orderGroupId
                                                                    ?.toInt() ??
                                                                0);
                                                  },
                                                );
                                              });
                                        },
                                      ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Vendor: ${state.offers.buyerName}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (showQuantity())
                              hideProductName()
                                  ? const SizedBox()
                                  : const SizedBox(height: 4),
                            if (showQuantity())
                              hideProductName()
                                  ? const SizedBox()
                                  : Text(
                                      'Quantity: ${baiProductsRes.content?[0].quantity?.toString()} ${baiProductsRes.content?[0].optionName}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                          ],
                        ),
                      ),
                      Container(
                        height: 64,
                        color: AppColors.white,
                        child: Container(
                          margin: const EdgeInsets.fromLTRB(0, 16, 0, 8),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: [
                                  TabItem(
                                    key: key1,
                                    text: "Quotes",
                                    index: 0,
                                    selectedColor: AppColors.primaryColorOld,
                                    unselectedColor: AppColors.white,
                                    selectedTextColor: AppColors.white,
                                    unselectedTextColor: Colors.black,
                                    selectedIndex: _selectedIndex,
                                    onTap: (index) {
                                      setState(() {
                                        _selectedIndex = index;
                                      });
                                    },
                                    fontSize: 14,
                                    width:
                                        MediaQuery.of(context).size.width / 2.2,
                                  ),
                                  TabItem(
                                    key: key2,
                                    text: "Negotiations",
                                    index: 1,
                                    selectedColor: AppColors.primaryColorOld,
                                    unselectedColor: AppColors.white,
                                    selectedTextColor: AppColors.white,
                                    unselectedTextColor: Colors.black,
                                    selectedIndex: _selectedIndex,
                                    onTap: (index) {
                                      setState(() {
                                        _selectedIndex = index;
                                      });
                                    },
                                    fontSize: 14,
                                    width:
                                        MediaQuery.of(context).size.width / 2.2,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Scrollable list section
                      Expanded(
                        child: RefreshIndicator(
                          onRefresh: () {
                            context.read<QuotesOnlyCubit>().loadQuotesSeller(
                                  widget.prchOrdrId.toString(),
                                  req,
                                  0,
                                );
                            return Future.value();
                          },
                          child: isLoading // Using the state's isLoading
                              ? const Center(
                                  child:
                                      CircularProgressIndicator()) // Centered loading indicator
                              : _buildOfferList(state, allowNego),
                        ),
                      ),
                      // Fixed bottom button section
                      if (enableSubmit && _selectedIndex == 0)
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 8.0), // Padding above the button row
                          child: Row(
                            children: [
                              if (state.offers.isEnableGst ?? false)
                                Expanded(
                                  flex: 1,
                                  child: TextField(
                                    controller: gstController,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\d+\.?\d{0,2}'))
                                    ],
                                    decoration: const InputDecoration(
                                      hintText: 'GST',
                                      hintStyle: TextStyle(
                                          fontWeight: FontWeight.bold),
                                      isDense: true,
                                      labelText: "GST",
                                      floatingLabelAlignment:
                                          FloatingLabelAlignment.center,
                                      suffix: Text("%"),
                                      labelStyle: TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      border: OutlineInputBorder(),
                                    ),
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                              if (state.offers.isEnableGst ?? false)
                                const SizedBox(width: 8),
                              if (!keyboardVisible)
                                Expanded(
                                  flex: 2,
                                  child: BaiButton(
                                    onTap: () {
                                      if (state.offers.isEnableGst ?? false) {
                                        var gstValue =
                                            double.tryParse(gstController.text);
                                        if (gstValue == null ||
                                            gstValue < 0 ||
                                            gstValue > 100) {
                                          alert(
                                              "Please input a valid GST percentage between 0 and 100");
                                          return;
                                        }
                                      }
                                      _showConfirmationDialog(context);
                                    },
                                    text: "SAVE",
                                  ),
                                ),
                            ],
                          ),
                        ),
                      if (_selectedIndex == 1 &&
                          sellerOffers
                              .where((e) =>
                                  getAlreadySubmittedQuote(
                                      e, alreadySubmittedQuotes) !=
                                  null)
                              .toList()
                              .isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 8.0), // Padding above the button
                          child: BaiButton(
                            onTap: () {
                              _notifyBuyer(context);
                            },
                            text: "NOTIFY BUYER",
                            backgoundColor: AppColors.primaryColorOld,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }
            return const SizedBox(); // Should not happen if states are handled
          },
        ),
      ),
    );
  }

  Widget _buildOfferList(SellerQuotesLoaded state, bool allowNego) {
    final negotiations = sellerOffers
        .where(
            (e) => getAlreadySubmittedQuote(e, alreadySubmittedQuotes) != null)
        .toList();
    final displayList = _selectedIndex == 0 ? sellerOffers : negotiations;

    if (!displayList.isNotEmpty) {
      return SizedBox(
        height: MediaQuery.of(context).size.height / 1.5,
        child: Center(
          child: Text(
            _selectedIndex == 0 ? "No quotes found" : "No negotiations found",
            style: largeStyle,
          ),
        ),
      );
    }

    return ListView.separated(
      itemCount: displayList.length,
      padding: const EdgeInsets.symmetric(
        horizontal: 2,
        vertical: 1,
      ),
      itemBuilder: (BuildContext context, int index) {
        var item = displayList[index];

        if (_selectedIndex == 1) {
          // For negotiations tab
          final submittedQuote = getAlreadySubmittedQuote(
              item, state.offers.alreadySubmittedQuote ?? []);
          if (submittedQuote != null) {
            final offer = convertAlreadySubmittedToOffer(submittedQuote);
            return NegotiationCard(
              offerDetails: offer,
              onAccept: (offerDetails) {
                // Handle accept if needed
              },
              onNegotiate: (offerDetails) {
                if (!allowNego) {
                  properAlert("Negotiation not allowed.");
                  return;
                }
                showDialog(
                  context: context,
                  builder: (context) {
                    return NegotiateDialog(
                      offer: offerDetails,
                      unit: extractUnit(offerDetails),
                    );
                  },
                );
              },
              showQuantity: showQuantity(),
            );
          }
          return const SizedBox(); // Skip if no submitted quote
        }

        // For quotes tab
        return SellerCard(
          key: ValueKey(index),
          finalofferController: finalofferControllers[index],
          number: index + 1,
          offer: item,
          item: baiProductsRes.content![0],
          offerController: offerControllers[index],
          commentController: commentControllers[index],
          quantityController: quantityControllers[index],
          showQuantity: showQuantity(),
          quantity: getQuantity(item),
          unit: getUnit(item),
          gst: currentGstValue ?? fullResponse?.productGst?.toDouble(),
          onImagesUpdated: (images) {
            setState(() {
              this.images[index] = images;
            });
          },
          onFilesUpdated: (files) {
            setState(() {
              this.files[index] = files;
            });
          },
          onAudiosUpdated: (audios) {
            setState(() {
              this.audios[index] = audios;
            });
          },
          submittedQuote: getAlreadySubmittedQuote(
              item, state.offers.alreadySubmittedQuote ?? []),
          previousQuote:
              getPreviousQuote(item, state.offers.previousQuotes ?? []),
          images: images[index],
          files: files[index],
          audios: audios[index],
          onSubmit: () {
            _showConfirmationDialog(context);
          },
          imageBytes: imageBytes[index],
          fileBytes: fileBytes[index],
          onImageBytesUpdated: (bytes) {
            setState(() {
              imageBytes[index] = bytes;
            });
          },
          onFileBytesUpdated: (bytes) {
            setState(() {
              fileBytes[index] = bytes;
            });
          },
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return const SizedBox(height: 12);
      },
    );
  }

  bool showSubmitButton() {
    return true;
  }

  AlreadySubmittedQuote? getAlreadySubmittedQuote(
      SellerOffer offer, List<AlreadySubmittedQuote> prevQuotes) {
    for (var quote in prevQuotes) {
      if (quote.variant1OptionId == offer.variant1OptionId &&
          quote.variant2OptionId == offer.variant2OptionId &&
          quote.variant3OptionId == offer.variant3OptionId) {
        return quote;
      }
    }
    return null;
  }

  PreviousQuote? getPreviousQuote(
      SellerOffer offer, List<PreviousQuote> prevQuotes) {
    for (var quote in prevQuotes) {
      if (quote.variant1OptionId == offer.variant1OptionId &&
          quote.variant2OptionId == offer.variant2OptionId &&
          quote.variant3OptionId == offer.variant3OptionId) {
        return quote;
      }
    }
    return null;
  }

  Widget _buildHeader() {
    return Container(
      height: 52,
      decoration: BoxDecoration(
        color: AppColors.primaryColorOld,
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.summarize,
            color: Colors.white,
            size: 28,
          ),
          SizedBox(width: 8),
          Text(
            'Quotes',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showConfirmationDialog(BuildContext context) {
    var style = const TextStyle(
      fontWeight: FontWeight.bold,
      color: Colors.black,
    );
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            "Confirm Submission",
            style: style,
          ),
          content: const Text(
              "Before submitting, please confirm that the discount and transportation charges are filled in on the Quote Summary page.\n\n Do you want to proceed?"),
          actions: <Widget>[
            TextButton(
              child: Text(
                "No",
                style: style,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                "Yes",
                style: style,
              ),
              onPressed: () {
                print('--------onPressed---1');
                Navigator.of(context).pop();
                Navigator.of(context).pop();

                onSubmit();
                // context.read<PostSellerCubit>().postSeller();

                //llllll
              },
            ),
          ],
        );
      },
    );
  }

  void _notifyBuyer(BuildContext context) {
    // Get the current negotiation offers
    final negotiations = sellerOffers
        .where(
            (e) => getAlreadySubmittedQuote(e, alreadySubmittedQuotes) != null)
        .toList();

    if (negotiations.isEmpty) {
      alert("No negotiations found to notify about.");
      return;
    }

    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            "Notify Buyer",
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          content: const Text(
              "This will send a notification to the buyer about the negotiation. Do you want to proceed?"),
          actions: <Widget>[
            TextButton(
              child: const Text(
                "No",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text(
                "Yes",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();

                // Find the first negotiation with a submitted quote
                for (var item in negotiations) {
                  final submittedQuote =
                      getAlreadySubmittedQuote(item, alreadySubmittedQuotes);
                  if (submittedQuote != null) {
                    // Use the notifyBuyer function from OffersCubit
                    context
                        .read<OffersCubit>()
                        .notifyBuyer(submittedQuote.id ?? 0);
                    return;
                  }
                }

                alert("No valid negotiation ID found to notify about.");
              },
            ),
          ],
        );
      },
    );
  }

  String extractUnit(Offer item) {
    String input = "N/A";

    var content = baiProductsRes.content?[0];

    if ((content?.mvtItemName ?? "").toLowerCase().contains("reinforcement")) {
      int id = 0;
      if (item.variant1OptionGroupName?.toLowerCase() == "size") {
        id = 1;
      } else if (item.variant2OptionGroupName?.toLowerCase() == "size") {
        id = 2;
      } else if (item.variant3OptionGroupName?.toLowerCase() == "size") {
        id = 3;
      }

      switch (id) {
        case 1:
          input = item.variant1OptionName ?? "";
          break;
        case 2:
          input = item.variant2OptionName ?? "";
          break;
        case 3:
          input = item.variant3OptionName ?? "";
          break;
        default:
          input = "";
      }

      List<String> parts = input.split(' - ');
      if (parts.isNotEmpty) {
        List<String> lastPart = parts.last.split(' ');
        return lastPart.isNotEmpty ? lastPart.last : '';
      }
    } else {
      return content?.optionName ?? "N/A";
    }

    return '';
  }

  Offer convertAlreadySubmittedToOffer(AlreadySubmittedQuote quote) {
    return Offer(
      id: quote.id,
      variant1OptionGroupName: quote.variant1OptionGroupName,
      variant1OptionName: quote.variant1OptionName,
      variant2OptionGroupName: quote.variant2OptionGroupName,
      variant2OptionName: quote.variant2OptionName,
      variant3OptionGroupName: quote.variant3OptionGroupName,
      variant3OptionName: quote.variant3OptionName,
      variant1OptionId: quote.variant1OptionId,
      variant2OptionId: quote.variant2OptionId,
      variant3OptionId: quote.variant3OptionId,
      negotiationHistory: quote.negotiationHistory,
      offerPrice: quote.offerPrice,
      quantity: quote.quantity,
      remarks: quote.remarks,
      statusCd: quote.statusCd,
      statusName: quote.statusName,
      createdAt: quote.createdAt,
      gst: quote.gst,
    );
  }

  @override
  void dispose() {
    gstController.dispose();
    super.dispose();
  }
}
